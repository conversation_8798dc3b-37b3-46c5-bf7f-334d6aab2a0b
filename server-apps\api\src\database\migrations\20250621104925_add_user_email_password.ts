import type { Kysely } from 'kysely';
import type { Database } from '../database.types';

export async function up(db: Kysely<Database>): Promise<void> {
    await db.schema
        .alterTable('user_ref')
        .addColumn('email', 'varchar', (col) => col.notNull().unique())
        .addColumn('password_hash', 'varchar', (col) => col.notNull())
        .execute();
}

export async function down(db: Kysely<Database>): Promise<void> {
    await db.schema
        .alterTable('user_ref')
        .dropColumn('email')
        .dropColumn('password_hash')
        .execute();
}
