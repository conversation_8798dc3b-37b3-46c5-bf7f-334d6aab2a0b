import { Injectable } from '@angular/core';
import { Optional, type UUID } from 'lib/types/common.no-deps';
import type { TaskReport, TaskReportStatus } from 'lib/types/task.no-deps';
import { db } from '../../../core/utils/database.util';

@Injectable({ providedIn: 'root' })
export class ReportRepository {
    fetchAll(): Promise<TaskReport[]> {
        return db.reports.toArray();
    }

    fetchSince(since: string): Promise<TaskReport[]> {
        const sinceDate = new Date(since);
        return this.fetchAll().then((reports) =>
            reports.filter(
                (report) => new Date(report.lastModifiedAt) > sinceDate
            )
        );
    }

    fetchById(id: UUID): Promise<Optional<TaskReport>> {
        return db.reports.get(id).then(Optional.ofNullable);
    }

    fetchByIds(ids: UUID[]): Promise<TaskReport[]> {
        return db.reports
            .bulkGet(ids)
            .then((reports) => reports.filter((report) => !!report));
    }

    fetchByStatus(status: TaskReportStatus): Promise<TaskReport[]> {
        return db.reports.where({ status }).toArray();
    }

    createBulk(reports: TaskReport[]): Promise<UUID[]> {
        return db.reports.bulkAdd(reports, { allKeys: true });
    }

    updateBulk(reports: TaskReport[]): Promise<UUID[]> {
        // TODO See if `.bulkPut` can be replaced with `.bulkUpdate`.
        return db.reports.bulkPut(reports, { allKeys: true });
    }

    updateStatus(id: UUID, status: TaskReportStatus): Promise<void> {
        return db.reports.update(id, { status }).then();
    }

    async updateAnswers(
        reportId: UUID,
        answers: TaskReport['answers'],
        userId: number
    ): Promise<void> {
        return db.reports
            .update(reportId, {
                answers,
                lastModifiedAt: new Date().toISOString(),
                lastModifiedBy: userId,
            })
            .then();
    }
}
