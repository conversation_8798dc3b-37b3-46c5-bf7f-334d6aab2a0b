import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

const baseClasses =
    'rounded-full shadow-[0_0.1rem_0.4rem_var(--color-black)] flex items-center justify-center cursor-pointer transition-all';

const colorClasses = {
    primary: 'bg-sky-600 hover:bg-sky-800 text-white',
    secondary: 'bg-purple-600 hover:bg-purple-700 text-white',
    accent: 'bg-pink-600 hover:bg-pink-700 text-white',
};

const iconSizeClasses = {
    small: 'h-5 w-5',
    medium: 'h-6 w-6',
    large: 'h-8 w-8',
};

function getSizeClasses(extended: boolean) {
    return {
        small: extended ? 'h-8 px-3' : 'h-10 w-10',
        medium: extended ? 'h-12 px-4' : 'h-14 w-14',
        large: extended ? 'h-16 px-5' : 'h-20 w-20',
    };
}

@Component({
    selector: 'app-floating-action-button',
    imports: [CommonModule],
    template: `
        <button [class]="buttonClasses" (click)="onClick()" aria-label="Add">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
                [class]="iconSizeClasses"
            >
                <path
                    fill-rule="evenodd"
                    d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                    clip-rule="evenodd"
                />
            </svg>

            <span *ngIf="extended" class="ml-2 font-medium">
                {{ extendedLabel }}
            </span>
        </button>
    `,
})
export class FloatingActionButtonComponent implements OnInit {
    @Input() color: 'primary' | 'secondary' | 'accent' = 'primary';
    @Input() extended = false;
    @Input() extendedLabel = '';
    @Input() size: 'small' | 'medium' | 'large' = 'medium';

    @Output() clicked = new EventEmitter<void>();

    buttonClasses = '';
    iconSizeClasses = '';

    ngOnInit(): void {
        this.buttonClasses = `${baseClasses} ${colorClasses[this.color]} ${getSizeClasses(this.extended)[this.size]}`;
        this.iconSizeClasses = iconSizeClasses[this.size];
    }

    onClick() {
        this.clicked.emit();
    }
}
