import { Component, Input, ContentChildren, QueryList } from '@angular/core';
import { GridLayoutItemComponent } from './grid-layout-item.component';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'app-grid-layout',
    standalone: true,
    template: `
        <div class="grid gap-4" [ngClass]="getGridClasses()">
            <ng-content></ng-content>
        </div>
    `,
    imports: [CommonModule],
})
export class GridLayoutComponent {
    @Input() columns: number = 3;
    @Input() gap: 'sm' | 'md' | 'lg' = 'md';
    @Input() responsive: boolean = true;
    @ContentChildren(GridLayoutItemComponent)
    items!: QueryList<GridLayoutItemComponent>;

    getGridClasses(): string {
        const gapClasses = {
            sm: 'gap-2',
            md: 'gap-4',
            lg: 'gap-6',
        };

        // If responsive is true, use responsive grid settings
        if (this.responsive) {
            return `grid-cols-1 sm:grid-cols-2 md:grid-cols-${Math.min(this.columns, 6)} ${gapClasses[this.gap]}`;
        }

        // Otherwise use fixed columns
        return `grid-cols-${Math.min(this.columns, 12)} ${gapClasses[this.gap]}`;
    }
}
