import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ShiftChangeoverComponent } from './shift-changeover.component';

describe('ShiftChangeoverComponent', () => {
    let component: ShiftChangeoverComponent;
    let fixture: ComponentFixture<ShiftChangeoverComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ShiftChangeoverComponent],
        }).compileComponents();

        fixture = TestBed.createComponent(ShiftChangeoverComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
