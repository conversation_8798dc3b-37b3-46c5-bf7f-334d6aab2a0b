import { InjectionToken } from '@angular/core';
import type { UUID } from 'lib/types/common.no-deps';
import type { AnswerValue } from 'lib/types/task.no-deps';
import type { Observable } from 'rxjs';

export interface ReportExecutionService {
    init(reportId: UUID): void;

    getAnswerValue(elementPath: UUID[]): Observable<AnswerValue>;

    setAnswerValue(elementPath: UUID[], value: AnswerValue): void;
}

export const REPORT_EXECUTION_SERVICE_TOKEN =
    new InjectionToken<ReportExecutionService>(
        'REPORT_EXECUTION_SERVICE_TOKEN'
    );
