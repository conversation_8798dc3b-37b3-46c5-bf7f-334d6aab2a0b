import { Injectable } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { type ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { filter, map } from 'rxjs/operators';

@Injectable({
    providedIn: 'root',
})
export class TitleFormatService {
    constructor(
        private readonly router: Router,
        private readonly title: Title
    ) {}

    init(): void {
        // Join all router titles.
        this.router.events
            .pipe(
                filter((event) => event instanceof NavigationEnd),
                map(() => this.router.routerState.root),
                map((root) => this.getRouteTitles(root)),
                map((titles) => titles.filter((title) => !!title).join(' | '))
            )
            .subscribe((fullTitle) => this.title.setTitle(fullTitle || ''));
    }

    private getRouteTitles(route: ActivatedRoute): string[] {
        const titles: string[] = [];

        if (route.snapshot.data?.['title']) {
            titles.push(route.snapshot.data['title']);
        }

        if (route.children) {
            for (const childRoute of route.children) {
                titles.push(...this.getRouteTitles(childRoute));
            }
        }

        return titles;
    }
}
