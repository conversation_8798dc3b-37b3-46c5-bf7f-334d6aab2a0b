import bcrypt from 'bcrypt';
import { Service } from 'diod';
import { generateUUID } from 'lib/util/uuid';
import type { Transaction } from '../../database/database.types';
import { AbstractUserRepository } from './abstract/user.repository';
import { AbstractUserService } from './abstract/user.service';

@Service()
export class UserService extends AbstractUserService {
    constructor(private readonly userRepository: AbstractUserRepository) {
        super();
    }

    // TODO Remove this helper function once auth is implemented.
    async fetchDummyUser(db: Transaction): Promise<number> {
        const users = await this.userRepository.fetchAllUsers(db);

        if (users.length > 0) {
            return users[0].id;
        }

        // Use a dummy email and a bcrypt hash for the password
        const email = '<EMAIL>';
        const passwordHash = bcrypt.hashSync('testpassword', 10);
        return this.userRepository.createUser(db, {
            shareId: generateUUID(),
            userName: 'test-user-1',
            email,
            passwordHash,
        });
    }
}
