import { type CreateTableBuilder, type Kysely, sql } from 'kysely';
import type { Database } from '../database.types';

function addCommonTaskColumns<T extends string>(
    builder: CreateTableBuilder<T>
): CreateTableBuilder<T> {
    return builder
        .addColumn('created_at', 'timestamptz', (col) =>
            col.notNull().defaultTo(sql`now()`)
        )
        .addColumn('created_by', 'integer', (col) =>
            col.references('user_ref.id').onDelete('cascade').notNull()
        )
        .addColumn('id', 'uuid', (col) => col.primaryKey())
        .addColumn('is_deleted', 'boolean', (col) =>
            col.notNull().defaultTo(false)
        )
        .addColumn('label', 'varchar')
        .addColumn('last_modified_at', 'timestamptz', (col) =>
            col.notNull().defaultTo(sql`now()`)
        )
        .addColumn('last_modified_by', 'integer', (col) =>
            col.references('user_ref.id').onDelete('cascade').notNull()
        );
}

export async function up(db: Kysely<Database>): Promise<void> {
    // Create tables
    // user_ref
    await db.schema
        .createTable('user_ref')
        .addColumn('id', 'serial', (col) => col.primaryKey())
        .addColumn('is_deleted', 'boolean', (col) =>
            col.notNull().defaultTo(false)
        )
        .addColumn('share_id', 'uuid', (col) => col.notNull().unique())
        .addColumn('user_name', 'varchar')
        .execute();

    // user_group
    await db.schema
        .createTable('user_group')
        .addColumn('id', 'serial', (col) => col.primaryKey())
        .addColumn('is_deleted', 'boolean', (col) =>
            col.notNull().defaultTo(false)
        )
        .addColumn('share_id', 'uuid', (col) => col.notNull().unique())
        .addColumn('group_name', 'varchar')
        .execute();

    // user_ref_user_group
    await db.schema
        .createTable('user_group_user_ref')
        .addColumn('user_group', 'integer', (col) =>
            col.references('user_group.id').onDelete('cascade').notNull()
        )
        .addColumn('user_ref', 'integer', (col) =>
            col.references('user_ref.id').onDelete('cascade').notNull()
        )
        .addUniqueConstraint('user_group_user_ref_unique', [
            'user_group',
            'user_ref',
        ])
        .execute();

    // task_template
    await addCommonTaskColumns(db.schema.createTable('task_template'))
        .addColumn('approval_method', 'varchar', (col) =>
            col.notNull().defaultTo('atLeastOne')
        )
        .addColumn('flow', 'jsonb', (col) =>
            col.notNull().defaultTo('{"elements":[]}')
        )
        .addColumn('status', 'varchar', (col) =>
            col.notNull().defaultTo('templateEditInProgress')
        )
        .addColumn('version', 'integer', (col) => col.notNull().defaultTo(1))
        .execute();

    // task_template_approval
    await db.schema
        .createTable('task_template_approval')
        .addColumn('created_at', 'timestamptz', (col) =>
            col.notNull().defaultTo(sql`now()`)
        )
        .addColumn('task_template', 'uuid', (col) =>
            col.references('task_template.id').onDelete('cascade').notNull()
        )
        .addColumn('user_ref', 'integer', (col) =>
            col.references('user_ref.id').onDelete('cascade').notNull()
        )
        .addUniqueConstraint('task_template_approval_unique', [
            'task_template',
            'user_ref',
        ])
        .execute();

    // task_template_approver_group
    await db.schema
        .createTable('task_template_approver_group')
        .addColumn('task_template', 'uuid', (col) =>
            col.references('task_template.id').onDelete('cascade').notNull()
        )
        .addColumn('user_group', 'integer', (col) =>
            col.references('user_group.id').onDelete('cascade').notNull()
        )
        .addUniqueConstraint('task_template_approver_group_unique', [
            'task_template',
            'user_group',
        ])
        .execute();

    // task_template_approver_user
    await db.schema
        .createTable('task_template_approver_user')
        .addColumn('task_template', 'uuid', (col) =>
            col.references('task_template.id').onDelete('cascade').notNull()
        )
        .addColumn('user_ref', 'integer', (col) =>
            col.references('user_ref.id').onDelete('cascade').notNull()
        )
        .addUniqueConstraint('task_template_approver_user_unique', [
            'task_template',
            'user_ref',
        ])
        .execute();

    // task_template_flow_archive
    await db.schema
        .createTable('task_template_flow_archive')
        .addColumn('flow', 'jsonb', (col) => col.notNull())
        .addColumn('task_template', 'uuid', (col) =>
            col.references('task_template.id').onDelete('cascade').notNull()
        )
        .addColumn('version', 'integer', (col) => col.notNull())
        .execute();

    // task_assignment
    await addCommonTaskColumns(db.schema.createTable('task_assignment'))
        .addColumn('assignees', 'jsonb', (col) =>
            col.notNull().defaultTo('{"groups":[],"users":[]}')
        )
        .addColumn('flow', 'jsonb', (col) => col.notNull())
        .addColumn('metadata', 'jsonb', (col) => col.notNull())
        .addColumn('status', 'varchar', (col) =>
            col.notNull().defaultTo('assignmentNotStarted')
        )
        .addColumn('template_id', 'uuid', (col) =>
            col.references('task_template.id').onDelete('cascade').notNull()
        )
        .addColumn('template_version', 'integer', (col) => col.notNull())
        .execute();

    // task_report
    await addCommonTaskColumns(db.schema.createTable('task_report'))
        .addColumn('answers', 'jsonb', (col) => col.notNull())
        .addColumn('assignment_id', 'uuid', (col) =>
            col.references('task_assignment.id').onDelete('cascade').notNull()
        )
        .addColumn('metadata', 'jsonb', (col) => col.notNull())
        .addColumn('status', 'varchar', (col) =>
            col.notNull().defaultTo('reportInProgress')
        )
        .execute();

    // procedure
    await db.schema
        .createTable('procedure')
        .addColumn('created_at', 'timestamptz', (col) =>
            col.notNull().defaultTo(sql`now()`)
        )
        .addColumn('created_by', 'integer', (col) =>
            col.references('user_ref.id').onDelete('cascade').notNull()
        )
        .addColumn('id', 'uuid', (col) => col.primaryKey())
        .addColumn('is_deleted', 'boolean', (col) =>
            col.notNull().defaultTo(false)
        )
        .addColumn('label', 'varchar')
        .addColumn('last_modified_at', 'timestamptz', (col) =>
            col.notNull().defaultTo(sql`now()`)
        )
        .addColumn('last_modified_by', 'integer', (col) =>
            col.references('user_ref.id').onDelete('cascade').notNull()
        )
        .addColumn('task_approval_method', 'varchar', (col) =>
            col.notNull().defaultTo('atLeastOne')
        )
        .execute();

    // order
    await db.schema
        .createTable('order')
        .addColumn('created_at', 'timestamptz', (col) =>
            col.notNull().defaultTo(sql`now()`)
        )
        .addColumn('customer', 'jsonb', (col) => col.notNull())
        .addColumn('id', 'uuid', (col) => col.primaryKey())
        .addColumn('origin', 'jsonb', (col) => col.notNull())
        .addColumn('requirements', 'text', (col) => col.notNull())
        .addColumn('status', 'varchar', (col) =>
            col.notNull().defaultTo('created')
        )
        .execute();
}

export async function down(db: Kysely<Database>): Promise<void> {
    // Drop tables in reverse creation order.
    await db.schema.dropTable('order').execute();
    await db.schema.dropTable('procedure').execute();
    await db.schema.dropTable('task_report').execute();
    await db.schema.dropTable('task_assignment').execute();
    await db.schema.dropTable('task_template_flow_archive').execute();
    await db.schema.dropTable('task_template_approver_user').execute();
    await db.schema.dropTable('task_template_approver_group').execute();
    await db.schema.dropTable('task_template_approval').execute();
    await db.schema.dropTable('task_template').execute();
    await db.schema.dropTable('user_ref_user_group').execute();
    await db.schema.dropTable('user_group').execute();
    await db.schema.dropTable('user_ref').execute();
}
