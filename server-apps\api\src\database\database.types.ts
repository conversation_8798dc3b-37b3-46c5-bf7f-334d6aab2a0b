import type { Generated, Transaction as K<PERSON>ransaction } from 'kysely';
import type { Nullable, UUID } from 'lib/types/common.no-deps';
import type { Order } from 'lib/types/order.no-deps';
import type {
    Answer,
    AssignmentFlow,
    AssignmentMetadata,
    TemplateApprovalMethod,
    TemplateFlow,
    TaskAssignmentStatus,
    TaskMembers,
    ReportMetadata,
    TaskReportStatus,
    TaskTemplateStatus,
} from 'lib/types/task.no-deps';

// NOTE: `Generated<>` columns have database defaults.

// Column definitions common to most tables
interface LastModMetaColumns {
    created_at: Generated<string>;
    created_by: number;
    last_modified_at: Generated<string>;
    last_modified_by: number;
}

// Order table
interface OrderTable {
    created_at: Generated<string>;
    customer: Order['customer'];
    id: UUID;
    origin: Order['origin'];
    requirements: string;
    status: Generated<Order['status']>;
}

// Procedure table
interface ProcedureTable extends LastModMetaColumns {
    id: UUID;
    is_deleted: Generated<boolean>;
    label: Nullable<string>;
    task_approval_method: Generated<AssignmentMetadata>;
}

// Common task columns
interface TaskCommonColumns extends LastModMetaColumns {
    id: UUID;
    is_deleted: Generated<boolean>;
    label: Nullable<string>;
}

// Task assignment tables
interface TaskAssignmentTable extends TaskCommonColumns {
    assignees: Generated<TaskMembers>;
    flow: AssignmentFlow;
    metadata: AssignmentMetadata;
    status: Generated<TaskAssignmentStatus>;
    template_id: UUID;
    template_version: number;
}

// Task report tables
interface TaskReportTable extends TaskCommonColumns {
    answers: Record<string, Answer>;
    assignment_id: UUID;
    metadata: ReportMetadata;
    status: Generated<TaskReportStatus>;
}

// Task template tables
interface TaskTemplateTable extends TaskCommonColumns {
    approval_method: Generated<TemplateApprovalMethod>;
    flow: Generated<TemplateFlow>;
    status: Generated<TaskTemplateStatus>;
    version: Generated<number>;
}

interface TaskTemplateApprovalTable {
    created_at: string;
    task_template: UUID;
    user_ref: number;
}

interface TaskTemplateApproverGroupTable {
    task_template: UUID;
    user_group: number;
}

interface TaskTemplateApproverUserTable {
    task_template: UUID;
    user_ref: number;
}

interface TaskTemplateFlowArchive {
    flow: TemplateFlow;
    task_template: UUID;
    version: number;
}

// User and group tables
interface UserGroupTable {
    id: Generated<number>;
    is_deleted: Generated<boolean>;
    group_name: string;
    share_id: UUID;
}

interface UserRefTable {
    id: Generated<number>;
    is_deleted: Generated<boolean>;
    user_name: string;
    share_id: UUID;
    email: string;
    password_hash: string;
}

interface UserGroupUserRefTable {
    user_group: number;
    user_ref: number;
}

export interface Database {
    // Order table
    order: OrderTable;
    // Procedure table
    procedure: ProcedureTable;
    // Task assignment tables
    task_assignment: TaskAssignmentTable;
    // Task report tables
    task_report: TaskReportTable;
    // Task template tables
    task_template: TaskTemplateTable;
    task_template_approval: TaskTemplateApprovalTable;
    task_template_approver_group: TaskTemplateApproverGroupTable;
    task_template_approver_user: TaskTemplateApproverUserTable;
    task_template_flow_archive: TaskTemplateFlowArchive;
    // User and group tables
    user_group: UserGroupTable;
    user_group_user_ref: UserGroupUserRefTable;
    user_ref: UserRefTable;
}

/**
 * Represents a transaction type that is specific to this database definition.
 */
export type Transaction = KTransaction<Database>;
