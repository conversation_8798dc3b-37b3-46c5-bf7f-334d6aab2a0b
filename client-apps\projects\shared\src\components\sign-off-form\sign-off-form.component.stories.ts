import { importProvidersFrom } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import type { Meta, StoryObj } from '@storybook/angular';
import { applicationConfig } from '@storybook/angular';
import SignaturePad from 'signature_pad';
import { SignOffFormComponent } from './sign-off-form.component';

const meta: Meta<SignOffFormComponent> = {
    title: 'Components/SignOffForm',
    component: SignOffFormComponent,
    decorators: [
        applicationConfig({
            providers: [
                importProvidersFrom(
                    FormsModule,
                    ReactiveFormsModule,
                    SignaturePad
                ),
            ],
        }),
    ],
    tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<SignOffFormComponent>;

export const Default: Story = {
    args: {},
};

export const WithPrefilledDate: Story = {
    args: {},
    render: (args) => ({
        props: {
            ...args,
            ngOnInit: function (this: SignOffFormComponent) {
                this['form'].patchValue({
                    date: '2024-01-01',
                    time: '12:00',
                });
            },
        },
    }),
};
