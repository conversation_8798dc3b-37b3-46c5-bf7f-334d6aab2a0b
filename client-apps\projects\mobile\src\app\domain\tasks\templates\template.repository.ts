import { Injectable } from '@angular/core';
import type { TaskTemplate } from 'lib/types/task.no-deps';
import { Optional, type UUID } from 'lib/types/common.no-deps';
import { db } from '../../../core/utils/database.util';

@Injectable({ providedIn: 'root' })
export class TemplateRepository {
    fetchAll(): Promise<TaskTemplate[]> {
        return db.templates.toArray();
    }

    fetchById(id: UUID): Promise<Optional<TaskTemplate>> {
        return db.templates.get(id).then(Optional.ofNullable);
    }

    fetchByIds(ids: UUID[]): Promise<TaskTemplate[]> {
        return db.templates
            .bulkGet(ids)
            .then((templates) => templates.filter((template) => !!template));
    }

    createBulk(templates: TaskTemplate[]): Promise<UUID[]> {
        return db.templates.bulkAdd(templates, { allKeys: true });
    }

    updateBulk(templates: TaskTemplate[]): Promise<UUID[]> {
        // TODO See if `.bulkPut` can be replaced with `.bulkUpdate`.
        return db.templates.bulkPut(templates, { allKeys: true });
    }
}
