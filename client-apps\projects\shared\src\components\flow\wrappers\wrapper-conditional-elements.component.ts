import { CommonModule } from '@angular/common';
import {
    Component,
    forwardRef,
    Inject,
    Input,
    On<PERSON><PERSON>roy,
    OnInit,
} from '@angular/core';
import type { UUID } from 'lib/types/common.no-deps';
import type {
    ElementCapture,
    ElementContainer,
    ElementEntity,
} from 'lib/types/flow.no-deps';
import type { AnswerValue } from 'lib/types/task.no-deps';
import type { Subscription } from 'rxjs';
import {
    REPORT_EXECUTION_SERVICE_TOKEN,
    ReportExecutionService,
} from '../../../tokens/report-execution.token';
import { getMatchingConditionalElements } from '../../../utils/conditional-element.util';
import { getMatchingTreeElements } from '../../../utils/conditional-tree-element.util';
import { ElementComponent } from '../element.component';

@Component({
    selector: 'app-wrapper-conditional-elements',
    imports: [
        CommonModule,
        // Loaded this way to circumvent circular dependency.
        forwardRef(() => ElementComponent),
    ],
    templateUrl: './wrapper-conditional-elements.component.html',
})
export class WrapperConditionalElementsComponent implements OnInit, OnDestroy {
    @Input({ required: true }) element!: ElementCapture | ElementContainer;
    @Input({ required: true }) elementPath!: UUID[];
    @Input({ required: true }) indent!: number;

    conditionalElements: ElementEntity[] = [];

    conditionalElementIndent!: number;

    private subscriptions: Subscription[] = [];

    private answers: Record<UUID, AnswerValue> = {};

    constructor(
        @Inject(REPORT_EXECUTION_SERVICE_TOKEN)
        protected readonly reportExecutionService: ReportExecutionService
    ) {}

    ngOnInit(): void {
        this.conditionalElementIndent = this.indent + 1;

        this.subscribeToElementValueChanges();
    }

    ngOnDestroy(): void {
        this.subscriptions.forEach((subscription) =>
            subscription.unsubscribe()
        );
    }

    getElementPath(id: UUID): UUID[] {
        return [...this.elementPath, id];
    }

    private subscribeToElementValueChanges = (): void => {
        switch (this.element.type) {
            case 'captureChoiceMultiple':
            case 'captureChoiceSingle':
            case 'captureNumber':
            case 'captureText': {
                const subscription = this.reportExecutionService
                    .getAnswerValue(this.elementPath)
                    .subscribe((answerValue) => {
                        this.answers[this.element.id] = answerValue;

                        this.reloadConditionalElementsIntoContainer();
                    });
                this.subscriptions = [subscription];
                break;
            }
            case 'containerSimple': {
                // Read all container child elements.
                const children = Object.values(this.element.children);
                // Filter out non-capture child elements.
                const captureChildren = this.getChildCaptureElements(children);
                // Create a subscription for every capture child element.
                this.subscriptions = captureChildren.map((child) => {
                    const childPath = [...this.elementPath, child.id];

                    return this.reportExecutionService
                        .getAnswerValue(childPath)
                        .subscribe((answerValue) => {
                            this.answers[child.id] = answerValue;

                            this.reloadConditionalElementsIntoContainer();
                        });
                });
                break;
            }
        }
    };

    private getChildCaptureElements = (
        elements: ElementEntity[]
    ): ElementCapture[] => {
        return elements.reduce<ElementCapture[]>((acc, element) => {
            switch (element.type) {
                case 'captureChoiceMultiple':
                case 'captureChoiceSingle':
                case 'captureNumber':
                case 'captureText':
                    acc.push(element);
            }
            return acc;
        }, []);
    };

    private reloadConditionalElementsIntoContainer = (): void => {
        // Get elements that should be displayed.
        const elements = Object.values(this.getMatchingConditionalElements());

        this.conditionalElements = elements;

        // this.conditionalElementsContainer.clear();

        // if (elements.length === 0) {
        //     // No conditions matched, so we don't show any.
        //     return;
        // }

        // // Load the conditional elements into view.
        // elements.forEach((element) => {
        //     const elementRef =
        //         this.conditionalElementsContainer.createComponent(
        //             ElementComponent
        //         );
        //     elementRef.instance.element = element;
        //     elementRef.instance.elementPath = [
        //         ...this.elementPath,
        //         element.id,
        //     ];
        //     elementRef.instance.indent = this.indent + 1;
        // });
    };

    private getMatchingConditionalElements = (): Record<
        UUID,
        ElementEntity
    > => {
        switch (this.element.type) {
            case 'captureChoiceMultiple':
            case 'captureChoiceSingle':
            case 'captureNumber':
            case 'captureText':
                return getMatchingConditionalElements(
                    this.element.conditionalChildren,
                    this.answers[this.element.id]
                );
            case 'containerSimple':
                return getMatchingTreeElements(
                    this.element.conditionalChildren,
                    (elementId) => this.answers[elementId]
                );
        }
    };
}
