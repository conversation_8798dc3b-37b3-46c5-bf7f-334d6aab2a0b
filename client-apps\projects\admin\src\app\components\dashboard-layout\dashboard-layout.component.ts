import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { DropdownMenuItemComponent } from '@shared/components/dropdown-menu/dropdown-menu-item.component';
import { DropdownMenuComponent } from '@shared/components/dropdown-menu/dropdown-menu.component';
import { ExitIconComponent } from '@shared/components/icons/exit.component';
import { PlusIconComponent } from '@shared/components/icons/plus.component';

interface MenuOption {
    action: () => void;
    icon: string;
    label: string;
}

@Component({
    selector: 'app-dashboard-layout',
    templateUrl: './dashboard-layout.component.html',
    styleUrl: './dashboard-layout.component.css',
    standalone: true,
    imports: [
        CommonModule,
        DropdownMenuComponent,
        DropdownMenuItemComponent,
        ExitIconComponent,
        PlusIconComponent,
    ],
})
export class DashboardLayoutComponent {
    @Input({ required: true }) title!: string;

    isDrawerOpen = false;

    toggleDrawer() {
        this.isDrawerOpen = !this.isDrawerOpen;
    }

    drawerMenuItems: MenuOption[] = [
        {
            icon: 'home',
            label: 'Home',
            action: () => console.log('Home clicked'),
        },
        {
            icon: 'chart-bar',
            label: 'Analytics',
            action: () => console.log('Analytics clicked'),
        },
        {
            icon: 'users',
            label: 'Users',
            action: () => console.log('Users clicked'),
        },
        {
            icon: 'cog',
            label: 'Settings',
            action: () => console.log('Settings clicked'),
        },
    ];

    headerActions: MenuOption[] = [
        {
            icon: 'bell',
            label: 'Notifications',
            action: () => console.log('Notifications clicked'),
        },
        {
            icon: 'search',
            label: 'Search',
            action: () => console.log('Search clicked'),
        },
        {
            icon: 'user-circle',
            label: 'Profile',
            action: () => console.log('Profile clicked'),
        },
    ];
}
