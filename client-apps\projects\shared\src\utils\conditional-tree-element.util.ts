import type { Nullable, UUID } from 'lib/types/common.no-deps';
import type {
    Condition,
    ExpressionTreeNode,
    LogicalOperator,
} from 'lib/types/construct.no-deps';
import type {
    ElementContainerConditionTree,
    ElementEntity,
} from 'lib/types/flow.no-deps';
import type { AnswerValue } from 'lib/types/task.no-deps';
import {
    evaluateNumberArrayCondition,
    evaluateNumberCondition,
    evaluateTextCondition,
} from './construct.util';

function verifyLogicalStatement(
    left: boolean,
    right: boolean,
    operator: LogicalOperator
): boolean {
    switch (operator) {
        case '&':
            return left && right;
        case '|':
            return left || right;
        case '~':
            return !(left && right);
    }
}

function evaluateCondition(
    condition: Condition,
    answerValue: Nullable<AnswerValue>
): boolean {
    if (!answerValue) {
        return false;
    }

    switch (answerValue.type) {
        case 'number': {
            if (condition.type !== 'number') {
                throw new Error(
                    `Expected condition to be of type 'number': ${condition.type}`
                );
            }
            return evaluateNumberCondition(condition, answerValue.value);
        }
        case 'numberArray': {
            if (condition.type !== 'numberArray') {
                throw new Error(
                    `Expected condition to be of type 'numberArray': ${condition.type}`
                );
            }
            return evaluateNumberArrayCondition(condition, answerValue.value);
        }
        case 'text': {
            if (condition.type !== 'text') {
                throw new Error(
                    `Expected condition to be of type 'text': ${condition.type}`
                );
            }
            return evaluateTextCondition(condition, answerValue.value);
        }
    }
}

function evaluateTreeNode(
    node: ExpressionTreeNode<UUID>,
    getValue: (path: UUID) => Nullable<AnswerValue>
): boolean {
    switch (node.type) {
        case 'inner': {
            const left = evaluateTreeNode(node.left, getValue);
            const right = evaluateTreeNode(node.right, getValue);
            return verifyLogicalStatement(left, right, node.operator);
        }
        case 'outer': {
            const value = getValue(node.id);
            return evaluateCondition(node.condition, value);
        }
    }
}

export function getMatchingTreeElements(
    conditionalElements: Record<UUID, ElementContainerConditionTree>,
    getValue: (elementId: UUID) => Nullable<AnswerValue>
): Record<UUID, ElementEntity> {
    // Find which conditional elements match the expected value.
    const matchingElements = Object.values(conditionalElements).filter((tree) =>
        evaluateTreeNode(tree.root, getValue)
    );

    // Aggregate all the child elements from all the matching conditional elements.
    return matchingElements.reduce<Record<UUID, ElementEntity>>(
        (acc, element) => {
            for (const [id, child] of Object.entries(element.children)) {
                if (!!acc[id]) {
                    throw new Error(
                        `Found two elements with the same ID: ${id}`
                    );
                }
                acc[id] = child;
            }
            return acc;
        },
        {}
    );
}
