<app-loading *ngIf="!report" [message]="'Loading report...'"></app-loading>
<div *ngIf="!!report" class="flex flex-col overflow-hidden bg-white">
    <div class="flex flex-row px-4 py-2 shadow-md z-10">
        <app-page-header
            [label]="report.assignment.label"
            (onLeftAction)="onBack()"
            (onRightAction)="onCompleteReport()"
        ></app-page-header>
    </div>
    <div class="flex flex-col p-4 overflow-auto">
        <app-flow [flow]="report!.assignment.flow"></app-flow>
    </div>
</div>