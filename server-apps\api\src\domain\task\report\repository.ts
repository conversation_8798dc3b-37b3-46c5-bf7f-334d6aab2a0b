import { Service } from 'diod';
import { sql } from 'kysely';
import type { UUID } from 'lib/types/common.no-deps';
import type {
    Answer,
    TaskReport,
    ReportMetadata,
    TaskReportStatus,
} from 'lib/types/task.no-deps';
import type { Database, Transaction } from '../../../database/database.types';
import { AbstractTaskReportRepository } from './abstract/abstract-repository';

type TableRow = Omit<
    Database['task_report'],
    'answers' | 'created_at' | 'is_deleted' | 'last_modified_at' | 'status'
> & {
    answers: Record<UUID, Answer>;
    created_at: string;
    is_deleted: boolean;
    last_modified_at: string;
    status: TaskReportStatus;
};

@Service()
export class TaskReportRepository extends AbstractTaskReportRepository {
    fetchAll(
        db: Transaction,
        opts: { excludeIds?: UUID[]; since?: string }
    ): Promise<TaskReport[]> {
        return db
            .selectFrom('task_report')
            .selectAll()
            .where((eb) =>
                // Apply `excludedIds` filter if option is present.
                opts.excludeIds
                    ? eb('id', 'not in', opts.excludeIds)
                    : eb.val(true)
            )
            .where((eb) =>
                // If `since` is given, then return newer records compared to it.
                opts.since
                    ? eb('last_modified_at', '>=', opts.since)
                    : eb.val(true)
            )
            .execute()
            .then((rows) => rows.map(this.readRow));
    }

    fetchByIds(db: Transaction, ids: UUID[]): Promise<TaskReport[]> {
        return db
            .selectFrom('task_report')
            .selectAll()
            .where('id', 'in', ids)
            .execute()
            .then((rows) => rows.map(this.readRow));
    }

    fetchByIdsOrSince(
        db: Transaction,
        ids: UUID[],
        since?: string
    ): Promise<TaskReport[]> {
        return db
            .selectFrom('task_report')
            .selectAll()
            .where((eb) =>
                since
                    ? eb('id', 'in', ids).or('last_modified_at', '>=', since)
                    : eb('id', 'in', ids)
            )
            .execute()
            .then((rows) => rows.map(this.readRow));
    }

    createBulk(db: Transaction, reports: TaskReport[]): Promise<UUID[]> {
        return db
            .insertInto('task_report')
            .values(
                reports.map((report) => ({
                    answers: report.answers,
                    assignment_id: report.assignmentId,
                    created_by: report.createdBy,
                    id: report.id,
                    label: report.label,
                    last_modified_by: report.lastModifiedBy,
                    metadata: report.metadata,
                    status: report.status,
                }))
            )
            .returning('id')
            .execute()
            .then((rows) => rows.map((row) => row.id));
    }

    updateBulk(db: Transaction, reports: TaskReport[]): Promise<UUID[]> {
        if (reports.length === 0) {
            return Promise.resolve([]);
        }

        // Define what columns need to be updated in the form of a select.
        const updateSelections = reports.map((report) =>
            db.selectNoFrom([
                sql<Record<UUID, Answer>>`${report.answers}::jsonb`.as(
                    'answers'
                ),
                sql<UUID>`${report.id}::uuid`.as('id'), // Needed to match existing records
                sql<string>`${report.lastModifiedAt}::timestamptz`.as(
                    'last_modified_at'
                ),
                sql<number>`${report.lastModifiedBy}::integer`.as(
                    'last_modified_by'
                ),
                sql<ReportMetadata>`${report.metadata}::jsonb`.as('metadata'),
                sql<TaskReportStatus>`${report.status}::varchar`.as('status'),
            ])
        );

        // Prepare a temporary update table (still a select).
        const tmpUpdateTable = updateSelections
            .slice(1)
            .reduce(
                (qb, selection) => qb.unionAll(selection),
                updateSelections[0]
            )
            .as('updates');

        // Perform the update.
        return db
            .updateTable('task_report as tr')
            .from(tmpUpdateTable)
            .set((eb) => ({
                answers: eb.ref('updates.answers'),
                last_modified_at: eb.ref('updates.last_modified_at'),
                last_modified_by: eb.ref('updates.last_modified_by'),
                metadata: eb.ref('updates.metadata'),
                status: eb.ref('updates.status'),
            }))
            .whereRef('tr.id', '=', 'updates.id')
            .returning('tr.id')
            .execute()
            .then((rows) => rows.map((row) => row.id));
    }

    private readRow = (row: TableRow): TaskReport => ({
        answers: row.answers,
        assignmentId: row.assignment_id,
        createdAt: row.created_at,
        createdBy: row.created_by,
        id: row.id,
        isDeleted: row.is_deleted,
        label: row.label,
        lastModifiedAt: row.last_modified_at,
        lastModifiedBy: row.last_modified_by,
        metadata: row.metadata,
        status: row.status,
    });
}
