import type { <PERSON>a, StoryObj } from '@storybook/angular';
import { ScreenOneComponent } from '../screen-one/screen-one.component';
import { ScreenThreeComponent } from '../screen-three/screen-three.component';
import { ScreenTwoComponent } from '../screen-two/screen-two.component';
import { ToggleButtonsComponent } from './toggle-buttons.component';

const meta: Meta<ToggleButtonsComponent> = {
    title: 'Components/Toggle Buttons',
    component: ToggleButtonsComponent,
    tags: ['autodocs'],
    decorators: [
        // Import necessary components for the story to work
        (story) => ({
            ...story,
            moduleMetadata: {
                imports: [
                    ScreenOneComponent,
                    ScreenTwoComponent,
                    ScreenThreeComponent,
                ],
            },
        }),
    ],
};

export default meta;
type Story = StoryObj<ToggleButtonsComponent>;

export const Default: Story = {
    args: {
        currentScreen: 'screen-one',
    },
};

export const StartWithScreenTwo: Story = {
    args: {
        currentScreen: 'screen-two',
    },
};

export const StartWithScreenThree: Story = {
    args: {
        currentScreen: 'screen-three',
    },
};
