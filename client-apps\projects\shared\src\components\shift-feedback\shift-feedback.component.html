<form (ngSubmit)="submitFeedback()">
  <div class="feedback-container">
    <div class="question" *ngFor="let item of [
      { label: 'Did you feel safe at all times today?', key: 'safety' },
      { label: 'Are you happy with the quality of product made today?', key: 'quality' },
      { label: 'Did all of the equipment work as expected today?', key: 'equipment' },
      { label: 'Do you think the team was motivated and engaged today?', key: 'motivation' }
    ]">
      <p>{{ item.label }}</p>
      <label *ngFor="let option of ['Yes', 'No', 'Maybe']">
        <input type="radio" [name]="item.key" [value]="option" [(ngModel)]="feedback[item.key]">
        {{ option }}
      </label>
    </div>

    <div class="notes-section">
      <label>Notes for the next shift</label>
      <textarea [(ngModel)]="feedback.notes" name="notes" placeholder="Enter notes"></textarea>
    </div>

    <button type="submit" class="submit-button">Submit Feedback</button>
  </div>
</form>