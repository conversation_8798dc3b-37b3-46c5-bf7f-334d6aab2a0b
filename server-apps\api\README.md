# API server

## Build

Create OpenAPI definitions - API server depends on these:
```bash
npm run tsoa
```

Build API server itself:
```bash
npm run build
```

## Database setup

You will need to have a running instance of PostgreSQL on your machine.

(Recommended) Use docker. Make sure you have docker set up locally, then run:

1) Start postgresql container using docker.
```bash
docker run -d -e POSTGRES_PASSWORD=postgres -p 5432:5432 -v pgdata:/var/lib/postgresql/data-api --name postgres-opexflow postgres:17-alpine
```

2) After your PostgreSQL instance is up and running, use your favorite SQL client to connect to it and create an empty database called `opexflow`.


3) Run migration script to set up the database:
```bash
npm run migrate-latest
```

## Run

```bash
npm run start
```