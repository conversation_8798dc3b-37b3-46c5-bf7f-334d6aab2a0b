import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SettingsButtonComponent } from './settings-button.component';

describe('SettingsButtonComponent', () => {
    let component: SettingsButtonComponent;
    let fixture: ComponentFixture<SettingsButtonComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [SettingsButtonComponent],
        }).compileComponents();

        fixture = TestBed.createComponent(SettingsButtonComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should have settings button with gear icon', () => {
        const button = fixture.nativeElement.querySelector('button.settings');
        const icon = fixture.nativeElement.querySelector('.gear-icon');

        expect(button).toBeTruthy();
        expect(icon).toBeTruthy();
        expect(icon.textContent).toContain('⚙');
    });

    it('should call onOpenSettings when clicked', () => {
        const consoleSpy = spyOn(console, 'log');
        const button = fixture.nativeElement.querySelector('button.settings');

        button.click();
        fixture.detectChanges();

        expect(consoleSpy).toHaveBeenCalledWith('Settings button clicked');
    });

    it('should have correct CSS class', () => {
        const button = fixture.nativeElement.querySelector('button');
        expect(button.classList.contains('settings')).toBeTruthy();
    });
});
