import { inject } from '@angular/core';
import type { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import type { ExecutableReport } from '@shared/types/report.type';
import { AssignmentService } from '../../domain/tasks/assignments/assignment.service';
import { ReportService } from '../../domain/tasks/reports/report.service';

export const reportResolver: ResolveFn<ExecutableReport> = async (
    route: ActivatedRouteSnapshot
) => {
    // Inject dependencies.
    const assignmentService = inject(AssignmentService);
    const reportService = inject(ReportService);

    // Read report ID from route params.
    const reportId = String(route.paramMap.get('reportId'));

    // Fetch report.
    const reportOpt = await reportService.fetchById(reportId);
    const report = reportOpt.orElseThrow(
        () => new Error(`Report not found: ${reportId}`)
    );

    // Fetch assignment associated with report.
    const assignmentId = report.assignmentId;
    const assignmentOpt = await assignmentService.fetchById(assignmentId);
    const assignment = assignmentOpt.orElseThrow(
        () => new Error(`Assignment not found: ${assignmentId}`)
    );

    return { assignment, report };
};
