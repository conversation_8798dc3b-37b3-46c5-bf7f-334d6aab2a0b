import type { Optional, Result, UUID } from 'lib/types/common.no-deps';
import type {
    AssignmentFlow,
    AssignmentMetadata,
    TaskAssignment,
    TaskAssignmentStatus,
    TaskMembers,
} from 'lib/types/task.no-deps';
import type { Transaction } from '../../../../database/database.types';

// Bodies
export interface CreateOneDTO {
    assignees?: TaskMembers;
    flow?: AssignmentFlow;
    label?: string;
    metadata: AssignmentMetadata;
    templateId: UUID;
}

export interface UpdateOneDTO extends Partial<CreateOneDTO> {
    status?: TaskAssignmentStatus;
}

export interface SyncDTO {
    assignments: TaskAssignment[];
    lastSyncTime?: string; // ISO 8601 date-time string
}

// Responses
export interface SyncResponseDTO {
    assignments: TaskAssignment[];
    requestTime: string; // ISO 8601 date-time string
}

export abstract class AbstractTaskAssignmentService {
    abstract fetchAll(
        db: Transaction,
        since?: string
    ): Promise<TaskAssignment[]>;

    abstract fetchById(
        db: Transaction,
        id: UUID
    ): Promise<Optional<TaskAssignment>>;

    abstract createOne(
        db: Transaction,
        userId: number,
        dto: CreateOneDTO
    ): Promise<Optional<UUID>>;

    abstract updateOne(
        db: Transaction,
        id: UUID,
        userId: number,
        dto: UpdateOneDTO
    ): Promise<Result<boolean, string>>;

    abstract deleteOne(
        db: Transaction,
        userId: number,
        id: UUID
    ): Promise<boolean>;

    abstract sync(db: Transaction, dto: SyncDTO): Promise<TaskAssignment[]>;
}
