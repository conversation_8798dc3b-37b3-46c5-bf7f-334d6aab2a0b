import { Component, Input, OnInit } from '@angular/core';
import type { ElementEntity } from 'lib/types/flow.no-deps';

@Component({
    selector: 'app-wrapper-element-common',
    templateUrl: './wrapper-element-common.component.html',
})
export class WrapperElementCommonComponent implements OnInit {
    @Input({ required: true }) element!: ElementEntity;
    @Input() indent: number = 0;

    classes = '';

    isIndentEven = true;

    ngOnInit(): void {
        this.isIndentEven = this.indent % 2 === 0;
    }
}
