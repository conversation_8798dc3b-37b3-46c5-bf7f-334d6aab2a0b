import { Meta, StoryObj } from '@storybook/angular';
import { BottomNavComponent } from './bottom-nav.component';

const meta: Meta<BottomNavComponent> = {
    title: 'Components/Bottom Navigation',
    component: BottomNavComponent,
    parameters: {
        layout: 'fullscreen',
    },
    argTypes: {
        items: [
            { label: 'Tasks', icon: 'calendar-icon' },
            { label: 'Search', icon: 'search-icon' },
            { label: 'Actions', icon: 'actions-icon' },
            {
                label: 'Notifications',
                icon: 'notifications-icon',
                notificationCount: 7,
            },
        ],
        notificationCount: {
            control: { type: 'number' },
        },
    },
};

export default meta;
type Story = StoryObj<BottomNavComponent>;

export const Mobile: Story = {
    parameters: {
        viewport: {
            defaultViewport: 'mobile1',
        },
    },
};

export const Desktop: Story = {
    parameters: {
        viewport: {
            defaultViewport: 'desktop',
        },
    },
};

export const WithCustomNotifications: Story = {
    args: {
        notificationCount: 15,
    },
};
