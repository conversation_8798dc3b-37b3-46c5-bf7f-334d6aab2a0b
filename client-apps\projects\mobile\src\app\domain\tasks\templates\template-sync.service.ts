import { Injectable } from '@angular/core';
import type { Nullable } from 'lib/types/common.no-deps';
import { AbstractSyncService } from '../../../core/services/abstract-sync.service';
import { HeartbeatService } from '../../../core/services/heartbeat.service';
import { HttpClientService } from '../../../core/services/http-client.service';
import { TemplateService } from './template.service';

@Injectable({ providedIn: 'root' })
export class TaskTemplateSyncService extends AbstractSyncService {
    constructor(
        heartbeatService: HeartbeatService,
        private readonly httpClient: HttpClientService,
        private readonly templateService: TemplateService
    ) {
        super(heartbeatService);
    }

    override async sync(
        lastSyncTime: Nullable<string>
    ): Promise<Nullable<string>> {
        try {
            const { requestTime, templates } = await (lastSyncTime !== null
                ? this.httpClient.fetchTemplates(lastSyncTime)
                : this.httpClient.fetchTemplates());

            if (templates.length > 0) {
                await this.templateService.upsertTemplates(templates);

                return requestTime;
            }

            return null;
        } catch (err) {
            console.error('Error syncing templates from server:', err);

            return null;
        }
    }
}
