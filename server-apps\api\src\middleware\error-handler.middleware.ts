import type { NextFunction, Request, Response } from 'express';
import { ValidateError } from 'tsoa';

export async function errorHandlerMiddleware(
    err: unknown,
    req: Request,
    res: Response,
    next: NextFunction
) {
    if (err instanceof ValidateError) {
        console.warn(`Caught Validation Error for ${req.path}:`, err.fields);
        res.status(422).json({
            message: 'Validation Failed',
            details: err?.fields,
        });
        return next(err);
    }

    if (err instanceof Error) {
        res.status(500).json({
            message: 'Something went wrong',
        });
    }
    return next(err);
}
