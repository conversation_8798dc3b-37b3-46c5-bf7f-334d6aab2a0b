import { Service } from 'diod';
import { sql } from 'kysely';
import { type UUID, Optional } from 'lib/types/common.no-deps';
import type { Order } from 'lib/types/order.no-deps';
import { generateUUID } from 'lib/util/uuid';
import type { Database, Transaction } from '../../database/database.types';
import {
    AbstractOrderRepository,
    type CreateOneDTO,
} from './abstract/abstract-repository';

type TableRow = Omit<Database['order'], 'created_at' | 'status'> & {
    created_at: string;
    status: Order['status'];
};

@Service()
export class OrderRepository implements AbstractOrderRepository {
    fetchAll(db: Transaction): Promise<Order[]> {
        return db
            .selectFrom('order')
            .selectAll()
            .execute()
            .then((rows) => rows.map(this.readRow));
    }

    fetchById(db: Transaction, id: UUID): Promise<Optional<Order>> {
        return db
            .selectFrom('order')
            .selectAll()
            .where('id', '=', id)
            .execute()
            .then(([row]) => Optional.ofNullable(row).map(this.readRow));
    }

    createOne(db: Transaction, dto: CreateOneDTO): Promise<UUID> {
        return db
            .insertInto('order')
            .values({
                created_at: sql`now()`,
                customer: dto.customer,
                id: generateUUID(),
                origin: dto.origin,
                requirements: dto.requirements,
                status: dto.status,
            })
            .returning('id')
            .executeTakeFirstOrThrow()
            .then((result) => result.id);
    }

    deleteOne(db: Transaction, id: UUID): Promise<boolean> {
        // TODO Change this to soft-delete.
        return db
            .deleteFrom('order')
            .where('id', '=', id)
            .executeTakeFirst()
            .then((result) => result.numDeletedRows > 0);
    }

    private readRow = (row: TableRow): Order => ({
        createdAt: row.created_at,
        customer: row.customer,
        id: row.id,
        origin: row.origin,
        requirements: row.requirements,
        status: row.status,
    });
}
