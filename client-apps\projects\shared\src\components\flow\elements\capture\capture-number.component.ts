import { CommonModule } from '@angular/common';
import {
    Component,
    EventEmitter,
    Inject,
    Input,
    OnInit,
    Output,
} from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import type { Nullable, UUID } from 'lib/types/common.no-deps';
import type { ElementCaptureNumber } from 'lib/types/flow.no-deps';
import { distinctUntilChanged } from 'rxjs/operators';
import {
    REPORT_EXECUTION_SERVICE_TOKEN,
    ReportExecutionService,
} from '../../../../tokens/report-execution.token';
import { WrapperConditionalElementsComponent } from '../../wrappers/wrapper-conditional-elements.component';

type ValueType = Nullable<number>;

@Component({
    selector: 'app-flow-capture-number',
    imports: [
        CommonModule,
        ReactiveFormsModule,
        WrapperConditionalElementsComponent,
    ],
    templateUrl: './capture-number.component.html',
})
export class FlowCaptureNumberComponent implements OnInit {
    @Input({ required: true }) element!: ElementCaptureNumber;
    @Input({ required: true }) elementPath!: UUID[];
    @Input({ required: true }) indent!: number;

    @Output() valueChanged: EventEmitter<ValueType> = new EventEmitter();

    formControl: FormControl<ValueType> = new FormControl(null);

    constructor(
        @Inject(REPORT_EXECUTION_SERVICE_TOKEN)
        private readonly reportExecutionService: ReportExecutionService
    ) {}

    ngOnInit(): void {
        this.reportExecutionService
            .getAnswerValue(this.elementPath)
            .subscribe((answerValue) => {
                if (answerValue.type !== 'number') {
                    throw new Error(
                        `Expected answer value to be of type "text": ${answerValue.type}`
                    );
                }

                // TODO Create form at this point.
                // Set value to stored value.
                this.formControl.setValue(answerValue.value);
            });

        // Pass value changed event onto listeners.
        this.formControl.valueChanges
            .pipe(distinctUntilChanged())
            .subscribe((value) => {
                this.valueChanged.emit(value);
            });
    }
}
