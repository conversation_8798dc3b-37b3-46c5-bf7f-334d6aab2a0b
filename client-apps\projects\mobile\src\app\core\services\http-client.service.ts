import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import type {
    TaskAssignment,
    TaskReport,
    TaskTemplate,
} from 'lib/types/task.no-deps';
import { lastValueFrom, timeout, type Observable } from 'rxjs';

interface FetchTemplatesResponse {
    requestTime: string; // ISO 8601 date-time string
    templates: TaskTemplate[];
}

interface SyncReportsResponse {
    assignments: TaskAssignment[];
    reports: TaskReport[];
    requestTime: string; // ISO 8601 date-time string
}

@Injectable({ providedIn: 'root' })
export class HttpClientService {
    // Heartbeat path
    private pathHeartbeat = '/heartbeat';

    // Task paths
    private pathTask = '/task';
    private pathTaskReport = `${this.pathTask}/report`;
    private pathTaskReportSync = `${this.pathTaskReport}/sync`;
    private pathTaskTemplate = `${this.pathTask}/template`;

    // Heartbeat timeout
    private timeoutRequestHeartbeat = 1000 * 15; // 15 seconds

    constructor(private readonly httpClient: HttpClient) {}

    fetchTemplates(since?: string): Promise<FetchTemplatesResponse> {
        return lastValueFrom(
            this.httpClient.get<FetchTemplatesResponse>(this.pathTaskTemplate, {
                params: since ? { since } : {},
            })
        );
    }

    getHeartbeat(): Observable<unknown> {
        return this.httpClient
            .get(this.pathHeartbeat)
            .pipe(timeout(this.timeoutRequestHeartbeat));
    }

    syncReports(
        assignments: TaskAssignment[],
        reports: TaskReport[],
        lastSyncTime?: string
    ): Promise<SyncReportsResponse> {
        return lastValueFrom(
            this.httpClient.post<SyncReportsResponse>(this.pathTaskReportSync, {
                assignments,
                lastSyncTime,
                reports,
            })
        );
    }
}
