import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Meta, StoryObj } from '@storybook/angular';
import { ShiftFeedbackComponent } from './shift-feedback.component';

const meta: Meta<ShiftFeedbackComponent> = {
    title: 'Components/ShiftFeedback',
    component: ShiftFeedbackComponent,
    decorators: [
        // Add necessary module imports for the component
        (story) => ({
            ...story,
            moduleMetadata: {
                imports: [CommonModule, FormsModule],
            },
        }),
    ],
    // Optional: Add parameters for styling or other configurations
    parameters: {
        layout: 'centered',
    },
    // Optional: Add component level argTypes if needed
    argTypes: {
        submitFeedback: { action: 'submitFeedback' },
    },
};

export default meta;

type Story = StoryObj<ShiftFeedbackComponent>;

// Default story
export const Default: Story = {
    args: {
        feedback: {
            safety: '',
            quality: '',
            equipment: '',
            motivation: '',
            notes: '',
        },
    },
};

// Story with pre-filled data
export const PreFilled: Story = {
    args: {
        feedback: {
            safety: 'Yes',
            quality: 'Yes',
            equipment: 'No',
            motivation: 'Maybe',
            notes: 'Equipment maintenance needed for station 3.',
        },
    },
};
