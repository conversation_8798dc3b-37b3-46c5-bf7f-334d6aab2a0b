<div class="grid grid-cols-[repeat(auto-fit,minmax(10rem,1fr))] w-full m-auto gap-2">
    <label 
        *ngFor="let option of options; trackBy: trackBy" 
        class="flex items-center p-2 gap-2 hover:bg-gray-200"
        [class.selected]="isOptionSelected(option.id)"
        [for]="id + '-' + option.id"
    >
    <input
        [type]="type"
        [name]="id"
        [id]="id + '-' + option.id"
        [value]="option.id"
        [checked]="isOptionSelected(option.id)"
        (change)="toggleOption(option.id)"
        class="h-4 w-4"
    />
    {{ option.label }}</label>
</div>