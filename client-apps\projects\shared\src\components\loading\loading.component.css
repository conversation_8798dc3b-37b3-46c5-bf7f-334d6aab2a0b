.spinner-overlay {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 1.2rem;
  z-index: 1000;
}

.spinner {
  border: min(4px, 0.5vw) solid transparent;
  border-top-color: var(--spinner-color, #3498db);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.message {
  margin: 1rem;
  text-align: center;
  font-size: clamp(1rem, 2.5vw, 1.2rem);
  padding: 0 1rem;
  max-width: 90%;
}

.close-btn {
  margin-top: clamp(0.5rem, 2vw, 1rem);
  padding: clamp(0.3rem, 1vw, 0.5rem) clamp(0.6rem, 2vw, 1rem);
  background: red;
  border: none;
  color: white;
  font-size: clamp(0.8rem, 2vw, 1rem);
  cursor: pointer;
  border-radius: 4px;
}

@media (max-width: 768px) {
  .spinner-overlay {
    padding: 1rem;
  }

  .close-btn {
    min-width: 44px;
    min-height: 44px;
  }
}