import { Component, Input, computed, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface Item<I> {
    id: I;
    label: string;
}

@Component({
    selector: 'app-task-selector',
    imports: [CommonModule, FormsModule],
    templateUrl: './task-selector.component.html',
    standalone: true,
})
export class TaskSelectorComponent<I> {
    // Input for the items to display
    @Input({ required: true }) items: Array<Item<I>> = [];

    // Signals for reactive state
    filterTerm = signal('');
    currentPage = signal(1);
    itemsPerPage = signal(10);

    // Computed filtered items
    filteredItems = computed(() =>
        this.items.filter((item) =>
            item.label.toLowerCase().includes(this.filterTerm().toLowerCase())
        )
    );

    // Computed paginated items
    paginatedItems = computed(() => {
        const startIndex = (this.currentPage() - 1) * this.itemsPerPage();
        const endIndex = startIndex + this.itemsPerPage();
        return this.filteredItems().slice(startIndex, endIndex);
    });

    // Computed total pages
    totalPages = computed(() => {
        return Math.ceil(this.filteredItems().length / this.itemsPerPage());
    });

    // Computed page numbers for pagination controls
    pageNumbers = computed(() => {
        const pages = [];
        const total = this.totalPages();
        const current = this.currentPage();
        const maxVisible = 5; // Maximum visible page numbers

        if (total <= maxVisible) {
            for (let i = 1; i <= total; i++) {
                pages.push(i);
            }
        } else {
            let start = Math.max(1, current - Math.floor(maxVisible / 2));
            let end = Math.min(total, start + maxVisible - 1);

            if (end - start + 1 < maxVisible) {
                start = end - maxVisible + 1;
            }

            if (start > 1) {
                pages.push(1);
                if (start > 2) {
                    pages.push(-1); // Ellipsis indicator
                }
            }

            for (let i = start; i <= end; i++) {
                if (i > 0 && i <= total) {
                    pages.push(i);
                }
            }

            if (end < total) {
                if (end < total - 1) {
                    pages.push(-1); // Ellipsis indicator
                }
                pages.push(total);
            }
        }

        return pages;
    });

    // Update filter term
    updateFilter(term: string) {
        this.filterTerm.set(term);
        this.currentPage.set(1); // Reset to first page when filtering
    }

    // Change page
    goToPage(page: number) {
        if (page >= 1 && page <= this.totalPages()) {
            this.currentPage.set(page);
        }
    }

    // Change items per page
    changeItemsPerPage(count: number) {
        this.itemsPerPage.set(count);
        this.currentPage.set(1); // Reset to first page when changing items per page
    }

    getPageSize(): number {
        return Math.min(
            this.currentPage() * this.itemsPerPage(),
            this.filteredItems().length
        );
    }
}
