<div class="flex flex-col flex-grow">
    <app-grid-layout>
        <div
            *ngFor="let report of reports | keyvalue"
            (click)="onClickReport(report.key)"
            class="h-full hover:cursor-pointer hover:shadow-lg hover:shadow-gray-400 shadow-md rounded-lg border-1 border-gray-200 overflow-hidden"
        >
            <app-task-card
                [datetime]="report.value.lastModifiedAt"
                [description]="report.value.label || ''"
                [icon]="'bolt'"
                [tags]="reportTags"
                [title]="{ color: 'blue', text: 'Done' }"
            ></app-task-card>
        </div>
    </app-grid-layout>
</div>