import { CommonModule } from '@angular/common';
import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
    SimpleListComponent,
    type ListItem,
} from '@shared/components/lists/simple-list.component';
import type { Nullable, UUID } from 'lib/types/common.no-deps';
import type { TaskTemplate } from 'lib/types/task.no-deps';
import type { Subscription } from 'rxjs';
import { NavigationService } from '../../core/services/navigation.service';
import { AssignmentService } from '../../domain/tasks/assignments/assignment.service';
import { ReportService } from '../../domain/tasks/reports/report.service';
import { TemplateService } from '../../domain/tasks/templates/template.service';

@Component({
    selector: 'app-new-bottom-sheet',
    imports: [CommonModule, SimpleListComponent],
    templateUrl: './new-assignment-bottom-sheet.component.html',
})
export class NewAssignmentBottomSheeetComponent implements OnInit, On<PERSON><PERSON>roy {
    @Input() goToTask: Nullable<(taskId: number) => void> = null;

    private templateMap: Map<UUID, TaskTemplate> = new Map();
    private templatesSubscription!: Subscription;

    constructor(
        private readonly assignmentService: AssignmentService,
        private readonly navigationService: NavigationService,
        private readonly reportService: ReportService,
        private readonly templateService: TemplateService
    ) {}

    get templates(): ListItem[] {
        return Array.from(this.templateMap.values()).map((template) => ({
            icon: 'workflow',
            id: template.id,
            subtitle: 'Task',
            title: template.label || '',
        }));
    }

    async ngOnInit() {
        this.templatesSubscription = this.templateService.templates$.subscribe(
            (templates) => {
                for (const template of templates) {
                    this.templateMap.set(template.id, template);
                }
            }
        );
        this.templateService.fetchAll();
    }

    ngOnDestroy(): void {
        this.templatesSubscription.unsubscribe();
    }

    async onClickTemplate(templateId: UUID): Promise<void> {
        const reportId = await this.createAdHocReport(templateId);

        this.onClose().then(() =>
            this.navigationService.routeToExecuteTask(reportId)
        );
    }

    onClose(): Promise<boolean> {
        // Remove overlay route.
        return this.navigationService.closeBottomSheet();
    }

    private createAdHocReport = async (templateId: UUID): Promise<UUID> => {
        const report = await this.reportService.createAdHocReport(templateId);

        await this.assignmentService.updateStatusById(
            report.assignmentId,
            'assignmentInProgress'
        );

        return report.id;
    };
}
