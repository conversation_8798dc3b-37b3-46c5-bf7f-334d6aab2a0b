<nav class="bottom-nav">
  <div *ngFor="let item of items">
    <div class="nav-item">
      <i class="icon {{ item.icon }}">
        <span *ngIf="!!item.notificationCount && item.notificationCount > 0" class="badge">{{ item.notificationCount }}</span>
      </i>
      <span>{{ item.label }}</span>
    </div>    
  </div>

  <!-- Floating Action Button -->
  <div class="fab">
    <i class="plus-icon">+</i>
  </div>
</nav>