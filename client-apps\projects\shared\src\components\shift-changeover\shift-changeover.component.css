.shift-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f5f5f5;
  padding: 10px 20px;
  border-radius: 8px;
  max-width: 100%;
  box-sizing: border-box;
  position: relative;
}

.title {
  font-size: 18px;
  font-weight: bold;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  margin: 0 10px;
  flex: 1;
  text-align: center;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  max-width: calc(100% - 100px);
  /* Prevent overlap with buttons */
}

.icon-button {
  background: white;
  border: 1px solid #ccc;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.3s;
  z-index: 1;
  /* Ensure buttons stay clickable */
}

.icon-button:hover {
  background: #e0e0e0;
}

.gear-icon {
  font-size: 16px;
}

/* Responsive styles */
@media screen and (max-width: 480px) {
  .shift-header {
    padding: 8px 12px;
  }

  .title {
    font-size: 16px;
  }

  .icon-button {
    width: 28px;
    height: 28px;
  }

  .gear-icon {
    font-size: 14px;
  }
}

@media screen and (max-width: 320px) {
  .shift-header {
    padding: 6px 8px;
  }

  .title {
    font-size: 14px;
  }

  .icon-button {
    width: 24px;
    height: 24px;
  }
}