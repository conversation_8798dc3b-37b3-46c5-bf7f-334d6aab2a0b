import { Meta, StoryFn } from '@storybook/angular';
import { LoadingComponent } from './loading.component';

export default {
    title: 'Components/Loading',
    component: LoadingComponent,
    tags: ['autodocs'],
    argTypes: {
        message: {
            control: 'text',
            description: 'Loading message to display',
        },
        size: {
            control: 'text',
            description: 'Size of the spinner',
        },
        color: {
            control: 'color',
            description: 'Color of the spinner',
        },
        hideSpinner: { action: 'hideSpinner' },
    },
} as Meta<LoadingComponent>;

const Template: StoryFn<LoadingComponent> = (args: any) => ({
    props: args,
});

export const Default = Template.bind({});
Default.args = {
    message: 'Loading...',
    size: '50px',
    color: '#3498db',
};

export const LargeSpinner = Template.bind({});
LargeSpinner.args = {
    message: 'Processing large file...',
    size: '100px',
    color: '#2ecc71',
};

export const CustomMessage = Template.bind({});
CustomMessage.args = {
    message: 'Please wait while we fetch your data...',
    size: '60px',
    color: '#e74c3c',
};

export const MobileView = Template.bind({});
MobileView.args = {
    message: 'Loading on mobile...',
    size: '80px',
    color: '#3498db',
};
MobileView.parameters = {
    viewport: {
        defaultViewport: 'mobile1',
    },
};

export const TabletView = Template.bind({});
TabletView.args = {
    message: 'Loading on tablet...',
    size: '100px',
    color: '#3498db',
};
TabletView.parameters = {
    viewport: {
        defaultViewport: 'tablet',
    },
};
