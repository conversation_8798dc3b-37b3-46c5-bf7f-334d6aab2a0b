<div class="container p-4">
    <div class="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 auto-rows-auto gap-4">
        <div
            *ngFor="let order of orders" class="h-full hover:cursor-pointer hover:shadow-lg hover:shadow-gray-400 shadow-md rounded-lg overflow-hidden"
            [class.bg-lime-50]="highlightedOrder === order.id"
        >
            <app-task-card
                [icon]="order.icon"
                [title]="order.title"
                [description]="order.description"
                [datetime]="order.datetime"
                [hint]="order.hint"
                [tags]="order.tags"
            >
                <app-dropdown-menu actionContent>
                    <ng-template #menuButton let-isOpen>
                        <button
                            [class.bg-gray-200]="isOpen"
                            class="flex items-center hover:bg-gray-200 space-x-2 px-4 py-2 transition-all"
                        >
                            |||
                        </button>
                    </ng-template>
                    <div menuContent class="w-48">
                        <app-dropdown-menu-item>
                            <button
                                (click)="assignOrder(order.id)"
                                class="flex items-center p-4 hover:bg-gray-100"
                            >
                                Assign to operators
                            </button>
                        </app-dropdown-menu-item>
                    </div>
                </app-dropdown-menu>
            </app-task-card>
        </div>
    </div>
</div>