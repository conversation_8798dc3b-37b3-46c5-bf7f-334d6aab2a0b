import { Injectable } from '@angular/core';
import { Optional, type UUID } from 'lib/types/common.no-deps';
import type { TaskAssignment } from 'lib/types/task.no-deps';
import { db } from '../../../core/utils/database.util';

@Injectable({ providedIn: 'root' })
export class AssignmentRepository {
    fetchAll(): Promise<TaskAssignment[]> {
        return db.assignments.toArray();
    }

    fetchSince(since: string): Promise<TaskAssignment[]> {
        const sinceDate = new Date(since);
        return this.fetchAll().then((assignments) =>
            assignments.filter(
                (assignment) => new Date(assignment.lastModifiedAt) > sinceDate
            )
        );
    }

    fetchById(id: UUID): Promise<Optional<TaskAssignment>> {
        return db.assignments.get(id).then(Optional.ofNullable);
    }

    fetchByIds(ids: UUID[]): Promise<TaskAssignment[]> {
        return db.assignments
            .bulkGet(ids)
            .then((assignments) =>
                assignments.filter((assignment) => !!assignment)
            );
    }

    createBulk(assignments: TaskAssignment[]): Promise<UUID[]> {
        return db.assignments.bulkAdd(assignments, {
            allKeys: true,
        });
    }

    updateBulk(assignments: TaskAssignment[]): Promise<UUID[]> {
        // TODO See if `.bulkPut` can be replaced with `.bulkUpdate`.
        return db.assignments.bulkPut(assignments, {
            allKeys: true,
        });
    }
}
