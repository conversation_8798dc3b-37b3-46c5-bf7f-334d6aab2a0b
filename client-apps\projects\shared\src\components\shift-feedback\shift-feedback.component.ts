import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
    imports: [CommonModule, FormsModule],
    selector: 'app-shift-feedback',
    templateUrl: './shift-feedback.component.html',
    styleUrls: ['./shift-feedback.component.css'],
})
export class ShiftFeedbackComponent {
    rating: number = 0;
    comments: string = '';

    setRating(value: number): void {
        this.rating = value;
    }

    updateComments(value: string): void {
        this.comments = value;
    }

    resetForm(): void {
        this.rating = 0;
        this.comments = '';
    }

    feedback: {
        safety: string;
        quality: string;
        equipment: string;
        motivation: string;
        notes: string;
        [key: string]: string;
    } = {
        safety: '',
        quality: '',
        equipment: '',
        motivation: '',
        notes: '',
    };

    submitFeedback() {
        console.log('Shift Feedback Submitted:', this.feedback);
        alert('Feedback submitted successfully!');
        // Additional submission logic can be added here (e.g., API call)
    }
}
