import {
    Component,
    Input,
    Output,
    EventEmitter,
    HostListener,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { animate, style, transition, trigger } from '@angular/animations';

@Component({
    selector: 'app-side-drawer',
    standalone: true,
    imports: [CommonModule],
    templateUrl: './side-drawer.component.html',
    animations: [
        trigger('drawerAnimation', [
            transition(
                ':enter',
                [
                    style({ transform: '{{transformEnter}}' }),
                    animate('200ms ease-out', style({ transform: 'none' })),
                ],
                { params: { transformEnter: 'translateX(-100%)' } }
            ),
            transition(
                ':leave',
                [
                    animate(
                        '200ms ease-in',
                        style({ transform: '{{transformLeave}}' })
                    ),
                ],
                { params: { transformLeave: 'translateX(-100%)' } }
            ),
        ]),
    ],
})
export class SideDrawerComponent {
    @Input() isOpen = false;
    @Input() position: 'left' | 'right' = 'left';
    @Input() title = '';
    @Output() closed = new EventEmitter<void>();

    get animationParams() {
        return {
            transformEnter:
                this.position === 'left'
                    ? 'translateX(-100%)'
                    : 'translateX(100%)',
            transformLeave:
                this.position === 'left'
                    ? 'translateX(-100%)'
                    : 'translateX(100%)',
        };
    }

    close() {
        this.closed.emit();
    }

    @HostListener('document:keydown.escape', ['$event'])
    handleEscapeKey(event: KeyboardEvent) {
        if (this.isOpen) {
            this.close();
        }
    }
}
