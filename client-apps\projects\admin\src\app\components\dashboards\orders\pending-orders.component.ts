import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { AccordionComponent } from '@shared/components/accordion/accordion.component';
import { AccordionItemComponent } from '@shared/components/accordion/accordion-item.component';
import { SideDrawerComponent } from '@shared/components/side-drawer/side-drawer.component';
import { TaskSelectorComponent } from '@shared/components/task-selector/task-selector.component';
import {
    type SelectableItem,
    UserSelectorComponent,
} from '@shared/components/user-selector/user-selector.component';
import type { Nullable, UUID } from 'lib/types/common.no-deps';
import type { Order } from 'lib/types/order.no-deps';
import { OrderService } from '../../../services/order.service';
import { type OrderItem, OrdersGridComponent } from './orders-grid.component';

@Component({
    selector: 'app-pending-orders',
    imports: [
        AccordionComponent,
        AccordionItemComponent,
        FormsModule,
        OrdersGridComponent,
        SideDrawerComponent,
        TaskSelectorComponent,
        UserSelectorComponent,
    ],
    template: `
        <div class="flex flex-grow flex-row justify-between">
            <div class="flex flex-grow flex-col">
                <app-orders-grid
                    [orders]="orders"
                    [highlightedOrder]="orderToBeAssigned"
                    (onAssignOrder)="openAssignOrderSideMenu($event)"
                ></app-orders-grid>
            </div>
            <app-side-drawer
                [isOpen]="!!orderToBeAssigned"
                position="right"
                title="Assign operators"
                (closed)="closeAssignOrderSideMenu()"
                class="bg-lime-50"
            >
                <app-accordion>
                    <app-accordion-item title="Select template">
                        <app-task-selector
                            [items]="taskSelectorItems"
                        ></app-task-selector>
                    </app-accordion-item>

                    <app-accordion-item title="Select assignees">
                        <app-user-selector
                            [items]="products"
                            [(ngModel)]="selectedProducts"
                            [itemsPerPage]="5"
                            placeholder="Select products"
                        >
                            <!-- Custom item template -->
                            <ng-template
                                #itemTemplate
                                let-item
                                let-selected="selected"
                            >
                                <div
                                    class="flex items-center justify-between w-full"
                                >
                                    <div>
                                        <div class="font-medium">
                                            {{ item.name }}
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            {{ item.price }} |
                                            {{ item.category }}
                                        </div>
                                    </div>
                                    @if (selected) {
                                        <span class="text-green-500">✓</span>
                                    }
                                </div>
                            </ng-template>

                            <!-- Custom selected item template -->
                            <ng-template #selectedItemTemplate let-item>
                                <div
                                    class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm flex items-center"
                                >
                                    <span class="font-medium">{{
                                        item.name
                                    }}</span>
                                    <span class="text-xs mx-1"
                                        >({{ item.price }})</span
                                    >
                                    <button
                                        class="text-green-600 hover:text-green-800 ml-1"
                                        (click)="
                                            item.remove();
                                            $event.stopPropagation()
                                        "
                                    >
                                        &times;
                                    </button>
                                </div>
                            </ng-template>
                        </app-user-selector>
                    </app-accordion-item>
                </app-accordion>
            </app-side-drawer>
        </div>
    `,
    styles: [
        `
            :host {
                display: flex;
                flex-direction: column;
                flex-grow: 1;
            }
        `,
    ],
})
export class PendingOrdersComponent implements OnInit {
    ordersMap: Map<Order['id'], OrderItem> = new Map();

    orderToBeAssigned: Nullable<UUID> = null;

    taskSelectorItems = [
        { id: 1, label: 'Item no 1' },
        { id: 2, label: 'Item no 2' },
        { id: 3, label: 'Item no 3' },
        { id: 4, label: 'Item no 4' },
        { id: 5, label: 'Item no 5' },
        { id: 6, label: 'Item no 6' },
        { id: 7, label: 'Item no 7' },
        { id: 8, label: 'Item no 8' },
        { id: 9, label: 'Item no 9' },
        { id: 10, label: 'Item no 10' },
        { id: 11, label: 'Item no 11' },
        { id: 12, label: 'Item no 12' },
        { id: 13, label: 'Item no 13' },
        { id: 14, label: 'Item no 14' },
        { id: 15, label: 'Item no 15' },
        { id: 16, label: 'Item no 16' },
        { id: 17, label: 'Item no 17' },
        { id: 18, label: 'Item no 18' },
        { id: 19, label: 'Item no 19' },
        { id: 20, label: 'Item no 20' },
        { id: 21, label: 'Item no 21' },
        { id: 22, label: 'Item no 22' },
        { id: 23, label: 'Item no 23' },
        { id: 24, label: 'Item no 24' },
        { id: 25, label: 'Item no 25' },
        { id: 26, label: 'Item no 26' },
        { id: 27, label: 'Item no 27' },
        { id: 28, label: 'Item no 28' },
        { id: 29, label: 'Item no 29' },
        { id: 30, label: 'Item no 30' },
        { id: 31, label: 'Item no 31' },
        { id: 32, label: 'Item no 32' },
        { id: 33, label: 'Item no 33' },
        { id: 34, label: 'Item no 34' },
        { id: 35, label: 'Item no 35' },
        { id: 36, label: 'Item no 36' },
        { id: 37, label: 'Item no 37' },
        { id: 38, label: 'Item no 38' },
        { id: 39, label: 'Item no 39' },
        { id: 40, label: 'Item no 40' },
        { id: 41, label: 'Item no 41' },
        { id: 42, label: 'Item no 42' },
        { id: 43, label: 'Item no 43' },
        { id: 44, label: 'Item no 44' },
        { id: 45, label: 'Item no 45' },
        { id: 46, label: 'Item no 46' },
        { id: 47, label: 'Item no 47' },
        { id: 48, label: 'Item no 48' },
        { id: 49, label: 'Item no 49' },
        { id: 50, label: 'Item no 50' },
        { id: 51, label: 'Item no 51' },
        { id: 52, label: 'Item no 52' },
        { id: 53, label: 'Item no 53' },
        { id: 54, label: 'Item no 54' },
        { id: 55, label: 'Item no 55' },
        { id: 56, label: 'Item no 56' },
    ];

    products = [
        { id: 'p1', name: 'Laptop', price: 999, category: 'Electronics' },
        { id: 'p2', name: 'Smartphone', price: 699, category: 'Electronics' },
        { id: 'p3', name: 'Headphones', price: 199, category: 'Accessories' },
        { id: 'p4', name: 'Monitor', price: 299, category: 'Electronics' },
        { id: 'p5', name: 'Keyboard', price: 99, category: 'Accessories' },
        { id: 'p6', name: 'Mouse', price: 49, category: 'Accessories' },
        { id: 'p7', name: 'Tablet', price: 399, category: 'Electronics' },
        { id: 'p8', name: 'Smartwatch', price: 249, category: 'Accessories' },
    ];

    selectedProducts: any[] = [];

    constructor(
        private readonly activatedRoute: ActivatedRoute,
        private readonly orderService: OrderService,
        private readonly router: Router
    ) {}

    get orders(): OrderItem[] {
        return Array.from(this.ordersMap.values());
    }

    ngOnInit(): void {
        // Subscribe to peding orders.
        this.orderService.orders$.subscribe((orders) =>
            orders.forEach((order) =>
                this.ordersMap.set(order.id, {
                    datetime: order.createdAt,
                    description: `${order.customer.label} - ${order.requirements}`,
                    icon: 'bolt',
                    id: order.id,
                    tags: [{ text: order.origin.label }],
                    title: { color: 'orange', text: 'Needs assignment' },
                    hint: order.origin.label,
                })
            )
        );

        // Set highlighted order ID from activated route.
        this.activatedRoute.queryParamMap.subscribe((params) => {
            this.orderToBeAssigned = params.get('orderId');
        });
    }

    openAssignOrderSideMenu(orderId: UUID): void {
        this.toggleAssignOrderSideMenu(orderId);
    }

    closeAssignOrderSideMenu(): void {
        this.toggleAssignOrderSideMenu(null);
    }

    onSelectionChange(items: SelectableItem[]): void {
        console.log('Selection changed:', items);
    }

    private toggleAssignOrderSideMenu = (orderId: Nullable<UUID>): void => {
        this.router.navigate(['orders', 'pending'], {
            queryParams: { orderId },
        });
    };
}
