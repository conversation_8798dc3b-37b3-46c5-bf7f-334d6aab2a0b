import {
    AfterViewInit,
    Component,
    Inject,
    Input,
    ViewChild,
    ViewContainerRef,
} from '@angular/core';
import type { UUID } from 'lib/types/common.no-deps';
import type {
    ElementCapture,
    ElementContainer,
    ElementDisplay,
    ElementEntity,
} from 'lib/types/flow.no-deps';
import type { AnswerValue } from 'lib/types/task.no-deps';
import {
    REPORT_EXECUTION_SERVICE_TOKEN,
    ReportExecutionService,
} from '../../tokens/report-execution.token';
import { FlowCaptureMultipleChoiceComponent } from './elements/capture/capture-choice-multiple.component';
import { FlowCaptureChoiceSingleComponent } from './elements/capture/capture-choice-single.component';
import { FlowCaptureNumberComponent } from './elements/capture/capture-number.component';
import { FlowCaptureTextComponent } from './elements/capture/capture-text.component';
import { FlowContainerSimpleComponent } from './elements/container/container-simple.component';
import { FlowDisplayTextComponent } from './elements/display/display-text.component';
import { WrapperElementCommonComponent } from './wrappers/wrapper-element-common.component';

@Component({
    selector: 'app-flow-element',
    imports: [WrapperElementCommonComponent],
    templateUrl: './element.component.html',
})
export class ElementComponent implements AfterViewInit {
    @Input({ required: true }) element!: ElementEntity;
    @Input({ required: true }) elementPath!: UUID[];
    @Input({ required: true }) indent!: number;

    @ViewChild('elementContainer', { read: ViewContainerRef })
    elementContainer!: ViewContainerRef;

    constructor(
        @Inject(REPORT_EXECUTION_SERVICE_TOKEN)
        private readonly reportExecutionService: ReportExecutionService
    ) {}

    ngAfterViewInit(): void {
        this.loadElementIntoContainer();
    }

    private loadElementIntoContainer = async (): Promise<void> => {
        switch (this.element.type) {
            case 'containerSimple':
                return this.loadContainerElementIntoContainer(this.element);
            case 'captureChoiceMultiple':
            case 'captureChoiceSingle':
            case 'captureNumber':
            case 'captureText':
                return this.loadCaptureElementIntoContainer(this.element);
            case 'displayText':
                return this.loadDisplayElementIntoContainer(this.element);
        }
    };

    private loadCaptureElementIntoContainer = (
        element: ElementCapture
    ): void => {
        switch (element.type) {
            case 'captureChoiceMultiple': {
                const elementRef = this.elementContainer.createComponent(
                    FlowCaptureMultipleChoiceComponent
                );
                elementRef.instance.element = element;
                elementRef.instance.elementPath = this.elementPath;
                elementRef.instance.indent = this.indent;
                elementRef.instance.valueChanged.subscribe((value) =>
                    this.updateCaptureElementValue({
                        value,
                        type: 'numberArray',
                    })
                );
                break;
            }
            case 'captureChoiceSingle': {
                const elementRef = this.elementContainer.createComponent(
                    FlowCaptureChoiceSingleComponent
                );
                elementRef.instance.element = element;
                elementRef.instance.elementPath = this.elementPath;
                elementRef.instance.indent = this.indent;
                elementRef.instance.valueChanged.subscribe((value) =>
                    this.updateCaptureElementValue({
                        value,
                        type: 'numberArray',
                    })
                );
                break;
            }
            case 'captureNumber': {
                const elementRef = this.elementContainer.createComponent(
                    FlowCaptureNumberComponent
                );
                elementRef.instance.element = element;
                elementRef.instance.elementPath = this.elementPath;
                elementRef.instance.indent = this.indent;
                elementRef.instance.valueChanged.subscribe((value) =>
                    this.updateCaptureElementValue({
                        value,
                        type: 'number',
                    })
                );
                break;
            }
            case 'captureText': {
                const elementRef = this.elementContainer.createComponent(
                    FlowCaptureTextComponent
                );
                elementRef.instance.element = element;
                elementRef.instance.elementPath = this.elementPath;
                elementRef.instance.indent = this.indent;
                elementRef.instance.valueChanged.subscribe((value) =>
                    this.updateCaptureElementValue({
                        value,
                        type: 'text',
                    })
                );
                break;
            }
        }
    };

    private loadContainerElementIntoContainer = (
        element: ElementContainer
    ): void => {
        switch (element.type) {
            case 'containerSimple': {
                const elementRef = this.elementContainer.createComponent(
                    FlowContainerSimpleComponent
                );
                elementRef.instance.element = element;
                elementRef.instance.elementPath = this.elementPath;
                elementRef.instance.indent = this.indent;
                break;
            }
        }
    };

    private loadDisplayElementIntoContainer = (
        element: ElementDisplay
    ): void => {
        switch (element.type) {
            case 'displayText': {
                const elementRef = this.elementContainer.createComponent(
                    FlowDisplayTextComponent
                );
                elementRef.instance.element = element;
                break;
            }
        }
    };

    private updateCaptureElementValue = (value: AnswerValue): void => {
        this.reportExecutionService.setAnswerValue(this.elementPath, value);
    };
}
