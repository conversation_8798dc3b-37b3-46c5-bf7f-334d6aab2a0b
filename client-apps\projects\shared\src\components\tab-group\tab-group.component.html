<div class="relative flex flex-col w-full">
    <div class="relative flex w-full">
        <div
            *ngFor="let tab of tabs; let i = index"
            [class.text-sky-600]="tab.id === activeTab"
            (click)="selectTab(tab.id)"
            class="flex-1 flex flex-col items-center justify-center py-2 cursor-pointer hover:text-sky-800"
        >
            <div class="flex flex-col items-center">
            <span *ngIf="tab.icon" class="text-2xl mb-1 h-6 w-6">
                <app-icon [type]="tab.icon"></app-icon>
            </span>
            <span class="text-sm">{{ tab.label }}</span>
            </div>
        </div>

        <!-- Active tab indicator -->
        <div 
            class="absolute bottom-0 h-1 bg-sky-600 transition-all duration-300"
            [@tabIndicator]
            [style.left]="(100 / tabs.length) * getActiveTabIndex() + '%'"
            [style.width]="100 / tabs.length + '%'"
        ></div>
    </div>

    <ng-content></ng-content>
</div>