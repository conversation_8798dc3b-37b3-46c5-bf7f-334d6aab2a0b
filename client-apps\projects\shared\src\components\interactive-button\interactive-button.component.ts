import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'app-interactive-button',
    standalone: true,
    templateUrl: './interactive-button.component.html',
    styleUrls: ['./interactive-button.component.css'],
    imports: [CommonModule],
})
export class InteractiveButtonComponent {
    @Input() size: 'small' | 'medium' | 'large' = 'medium'; // Default size
    @Input() color: string = '#3498db'; // Default button color
    @Input() label: string = 'Click Me'; // Default text

    @Output() buttonClick = new EventEmitter<void>(); // Emits when clicked

    handleClick(): void {
        this.buttonClick.emit();
    }
}
