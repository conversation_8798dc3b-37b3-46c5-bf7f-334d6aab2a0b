import { Injectable } from '@angular/core';
import type { Tag } from '@shared/types/tag.type';
import type { Order, OrdersFetchDTO } from 'lib/types/order.no-deps';
import { BehaviorSubject } from 'rxjs';
import { io } from 'socket.io-client';
import { apiUrl } from '../utils/api.util';

export interface AssignableOrder extends Order {
    tag: Tag;
}

@Injectable({ providedIn: 'root' })
export class OrderService {
    private ordersSubject = new BehaviorSubject<AssignableOrder[]>([]);

    orders$ = this.ordersSubject.asObservable();

    constructor() {
        this.start();
    }

    private start = (): void => {
        const socket = io(apiUrl);

        socket.on('fetchedAllOrders', (data: OrdersFetchDTO) =>
            this.saveOrders(...data.orders)
        );

        socket.on('orderCreated', (order: Order) => this.saveOrders(order));

        socket.emit('subscribe', 'orders');
    };

    private saveOrders = (...orders: Order[]): void => {
        const allOrders = this.ordersSubject.getValue();
        const newOrders = orders.map((order) => ({
            ...order,
            tag: { text: 'Needs assignment' },
        }));

        allOrders.push(...newOrders);

        this.ordersSubject.next(allOrders);
    };
}
