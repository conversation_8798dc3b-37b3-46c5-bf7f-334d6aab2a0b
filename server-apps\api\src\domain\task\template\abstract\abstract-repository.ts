import type { Optional, UUID } from 'lib/types/common.no-deps';
import type { TaskTemplate, TaskTemplateStatus } from 'lib/types/task.no-deps';
import type { Transaction } from '../../../../database/database.types';

export interface CreateOneDTO
    extends Partial<
        Pick<TaskTemplate, 'approvalMethod' | 'approvers' | 'flow' | 'label'>
    > {}

export interface UpdateOneDTO extends CreateOneDTO {
    status?: TaskTemplateStatus;
}

export abstract class AbstractTaskTemplateRepository {
    abstract fetchAll(
        db: Transaction,
        opts?: { since?: string }
    ): Promise<TaskTemplate[]>;

    abstract fetchById(
        db: Transaction,
        id: UUID,
        opts?: { excludeDeleted?: boolean }
    ): Promise<Optional<TaskTemplate>>;

    abstract createOne(
        db: Transaction,
        userId: number,
        dto: CreateOneDTO
    ): Promise<UUID>;

    abstract updateOne(
        db: Transaction,
        id: UUID,
        userId: number,
        dto: UpdateOneDTO
    ): Promise<boolean>;

    abstract deleteOne(
        db: Transaction,
        id: UUID,
        userId: number
    ): Promise<boolean>;
}
