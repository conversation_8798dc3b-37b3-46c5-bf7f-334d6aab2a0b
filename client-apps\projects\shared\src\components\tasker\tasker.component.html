<div class="tab-container">
  <div class="tab" [class.active]="activeTab === 'todo'" (click)="switchTab('todo')">
    To Do
    <span *ngIf="todoCount > 0" class="badge">{{ todoCount }}</span>
  </div>

  <div class="tab" [class.active]="activeTab === 'done'" (click)="switchTab('done')">
    Done
  </div>
</div>

<!-- Content Area -->
<div class="tab-content">
  <ng-container *ngIf="activeTab === 'todo'">
    <p>List of To-Do tasks...</p>
  </ng-container>

  <ng-container *ngIf="activeTab === 'done'">
    <p>List of Completed tasks...</p>
  </ng-container>
</div>