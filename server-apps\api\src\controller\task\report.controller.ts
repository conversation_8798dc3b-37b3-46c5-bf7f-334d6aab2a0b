import type { TaskAssignment, TaskReport } from 'lib/types/task.no-deps';
import { Body, Controller, Post, Request, Route, Tags } from 'tsoa';
import { container } from '../../diod.config';
import { AbstractTaskReportService } from '../../domain/task/report/abstract/abstract-service';
import type { ApiRequest } from '../../shared/request.type';

// Bodies
interface ReportSyncBody {
    assignments: TaskAssignment[];
    lastSyncTime?: string; // ISO 8601 date-time string
    reports: TaskReport[];
}

// Response
interface ReportSyncResponse {
    assignments: TaskAssignment[];
    requestTime: string; // ISO 8601 date-time string
    reports: TaskReport[];
}

@Route('task/report')
@Tags('task-report')
export class TaskReportController extends Controller {
    private readonly taskReportService: AbstractTaskReportService;

    constructor() {
        super();
        this.taskReportService = container.get(AbstractTaskReportService);
    }

    @Post('sync')
    sync(
        @Request() request: ApiRequest,
        @Body() body: ReportSyncBody
    ): Promise<ReportSyncResponse> {
        return this.taskReportService.sync(request.transaction, body);
    }
}
