import Dexie, { type Table } from 'dexie';
import type { UUID } from 'lib/types/common.no-deps';
import type {
    TaskAssignment,
    TaskReport,
    TaskTemplate,
} from 'lib/types/task.no-deps';

export class Database extends Dexie {
    assignments!: Table<TaskAssignment, UUID>;
    reports!: Table<TaskReport, UUID>;
    templates!: Table<TaskTemplate, UUID>;

    constructor() {
        super('opexflow');

        // Define database templates
        // Add reports, tasks, templates tables
        this.version(1).stores({
            assignments:
                'id, assignees, createdAt, createdBy, flow, isDeleted, lastModifiedAt, lastModifiedBy, metadata, templateId, templateVersion, status',
            reports:
                'id, answers, assignmentId, createdAt, createdBy, isDeleted, lastModifiedAt, lastModifiedBy, metadata, status',
            templates:
                'id, approval_method, createdAt, createdBy, flow, label, lastModifiedAt, lastModifiedBy, isDeleted, status, version',
        });
    }

    allTables() {
        // New tables should be added to this list.
        return [this.assignments, this.reports, this.templates];
    }
}

// A single database instance shared by all.
export const db = new Database();
