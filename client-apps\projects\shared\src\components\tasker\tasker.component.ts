import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'app-tasker',
    imports: [CommonModule],
    templateUrl: './tasker.component.html',
    styleUrl: './tasker.component.css',
})
export class TaskerComponent {
    activeTab: string = 'todo';
    todoCount: number = 8;

    switchTab(tab: string) {
        this.activeTab = tab;
    }
}
