// dropdown-menu.component.ts
import {
    Component,
    ContentChild,
    ElementRef,
    HostListener,
    Input,
    TemplateRef,
    booleanAttribute,
    signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'app-dropdown-menu',
    standalone: true,
    imports: [CommonModule],
    template: `
        <div class="relative inline-block text-left" #dropdownContainer>
            <div (mousedown)="toggleDropdown()" (touchstart)="toggleDropdown()">
                <!-- Custom button template -->
                <ng-container *ngIf="menuButtonTemplate; else defaultButton">
                    <ng-container
                        *ngTemplateOutlet="
                            menuButtonTemplate;
                            context: { $implicit: isOpen() }
                        "
                    ></ng-container>
                </ng-container>

                <!-- Default button fallback -->
                <ng-template #defaultButton>
                    <button
                        type="button"
                        class="inline-flex justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        [attr.aria-expanded]="isOpen()"
                    >
                        {{ buttonText }}
                        <svg
                            class="-mr-1 ml-2 h-5 w-5"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                            aria-hidden="true"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                clip-rule="evenodd"
                            />
                        </svg>
                    </button>
                </ng-template>
            </div>

            <div
                *ngIf="isOpen()"
                class="origin-top-right absolute right-0 rounded-md shadow-lg bg-white z-50"
                [class]="menuPosition === 'left' ? 'left-0' : 'right-0'"
            >
                <div
                    class="py-1"
                    role="menu"
                    aria-orientation="vertical"
                    aria-labelledby="options-menu"
                >
                    <ng-content select="[menuContent]"></ng-content>
                </div>
            </div>
        </div>
    `,
})
export class DropdownMenuComponent {
    @Input() buttonText = 'Options';
    @Input({ transform: booleanAttribute }) closeOnClickOutside = true;
    @Input({ transform: booleanAttribute }) closeOnItemClick = true;
    @Input() menuPosition: 'left' | 'right' = 'right';

    @ContentChild('menuButton') menuButtonTemplate: TemplateRef<any> | null =
        null;

    isOpen = signal(false);

    toggleDropdown(): void {
        this.isOpen.update((val) => !val);
    }

    closeDropdown(): void {
        this.isOpen.set(false);
    }

    @HostListener('document:click', ['$event'])
    onClickOutside(event: MouseEvent): void {
        if (!this.closeOnClickOutside) return;
        if (!this.elementRef.nativeElement.contains(event.target)) {
            this.isOpen.set(false);
        }
    }

    @HostListener('document:touchstart', ['$event'])
    onTouchOutside(event: TouchEvent): void {
        if (!this.closeOnClickOutside) return;
        if (!this.elementRef.nativeElement.contains(event.target)) {
            this.isOpen.set(false);
        }
    }

    constructor(private elementRef: ElementRef) {}
}
