import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { type Tab, TabGroupComponent } from '../tab-group/tab-group.component';

@Component({
    selector: 'app-tab-container',
    templateUrl: './tab-container.component.html',
    styleUrl: './tab-container.component.css',
    imports: [CommonModule, FormsModule, TabGroupComponent],
})
export class TabContainerComponent {
    @Input() tabs: Tab[] = [];
    @Input() activeTab: string = '';
    @Input() searchPlaceholder: string = 'Search...';
    @Input() showSearch: boolean = true;
    @Input() spacedEvenly: boolean = false;

    @Output() tabChange = new EventEmitter<string>();
    @Output() search = new EventEmitter<{ query: string; activeTab: string }>();

    searchQuery: string = '';

    setActiveTab(tabId: string): void {
        if (this.activeTab !== tabId) {
            this.tabChange.emit(tabId);
        }
    }

    onSearch(): void {
        this.search.emit({
            query: this.searchQuery,
            activeTab: this.activeTab,
        });
    }
}
