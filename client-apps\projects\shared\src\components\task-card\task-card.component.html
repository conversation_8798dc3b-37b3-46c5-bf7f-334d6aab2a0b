<div class="p-4 h-full flex flex-col">
    <!-- Row 1: Round icon and rounded text box with leading dot on red background -->
    <div class="flex items-center mb-3">
        <div class="flex flex-shrink-0 items-center justify-center w-10 h-10 rounded-full border-1 border-sky-600 mr-3 overflow-hidden">
            <app-icon class="w-6 h-6 object-contain text-sky-600" [type]="icon"></app-icon>
        </div>
        <div [class]="'flex items-center rounded-full px-3 py-1.5 relative ' + titleBoxBg">
            <span [class]="'w-2 h-2 rounded-full mr-2 flex-shrink-0 ' + titleDotBg"></span>
            <span class="text-sm text-gray-800 whitespace-nowrap overflow-hidden text-ellipsis">{{ title.text }}</span>
        </div>
        <div class="flex flex-grow"></div>
        <ng-content select="[actionContent]"></ng-content>
    </div>

    <!-- Row 2: Description text -->
    <div class="flex flex-grow text-sm leading-relaxed text-gray-800 mb-3">
        {{ description }}
    </div>

    <!-- Row 3: Hint text -->
    <div class="text-xs text-gray-500 mb-3 italic">
        {{ hint }}
    </div>

    <!-- Row 4: Date-time with leading flag icon -->
    <div class="flex items-center text-xs text-sky-600 mb-3">
        <app-icon-flag class="w-4 h-4 mr-2"></app-icon-flag>
        <span>{{ datetime }}</span>
    </div>

    <!-- Row 5: Separator -->
    <div class="h-px bg-gray-200 my-3"></div>

    <!-- Row 6: Inline list of tags -->
    <div class="flex flex-wrap gap-2">
        <div class="flex items-center bg-gray-100 rounded-full px-2.5 py-1" *ngFor="let tag of tags">
            <app-icon *ngIf="!!tag.icon" [type]="tag.icon" class="w-3.5 h-3.5 mr-1.5 text-gray-500"></app-icon>
            <span class="text-xs text-gray-600">{{ tag.text }}</span>
        </div>
    </div>
</div>