import { Optional, type UUID } from 'lib/types/common.no-deps';
import {
    AbstractUserRepository,
    type CreateUserDto,
    type FetchUserDto,
} from './abstract/user.repository';
import type { Database, Transaction } from '../../database/database.types';

type FetchUserRow = {
    id: number;
    share_id: UUID;
    email: string;
    password_hash: string;
};

type UpdateUserDTO = Partial<
    Pick<CreateUserDto, 'userName' | 'email' | 'passwordHash'>
>;

type UpdateUserValues = Partial<
    Pick<Database['user_ref'], 'user_name' | 'email' | 'password_hash'>
>;

export class UserRepository extends AbstractUserRepository {
    private toFetchUserDto = (row: FetchUserRow): FetchUserDto => ({
        id: row.id,
        shareId: row.share_id,
        email: row.email,
        passwordHash: row.password_hash,
    });

    private getUpdateUserValues = (dto: UpdateUserDTO): UpdateUserValues => {
        const values: UpdateUserValues = {};
        if (dto.userName) {
            values.user_name = dto.userName;
        }
        if (dto.email) {
            values.email = dto.email;
        }
        if (dto.passwordHash) {
            values.password_hash = dto.passwordHash;
        }
        return values;
    };

    fetchAllUsers(db: Transaction): Promise<FetchUserDto[]> {
        return db
            .selectFrom('user_ref')
            .select(['id', 'share_id', 'email', 'password_hash'])
            .execute()
            .then((rows) => rows.map(this.toFetchUserDto));
    }

    fetchUserById(
        db: Transaction,
        userId: number
    ): Promise<Optional<FetchUserDto>> {
        return db
            .selectFrom('user_ref')
            .select(['id', 'share_id', 'email', 'password_hash'])
            .where('id', '=', userId)
            .execute()
            .then(([row]) => Optional.ofNullable(row).map(this.toFetchUserDto));
    }

    fetchUserByEmail(
        db: Transaction,
        email: string
    ): Promise<Optional<FetchUserDto>> {
        return db
            .selectFrom('user_ref')
            .select(['id', 'share_id', 'email', 'password_hash'])
            .where('email', '=', email)
            .execute()
            .then(([row]) => Optional.ofNullable(row).map(this.toFetchUserDto));
    }

    createUser(db: Transaction, dto: CreateUserDto): Promise<number> {
        return db
            .insertInto('user_ref')
            .values({
                share_id: dto.shareId,
                user_name: dto.userName,
                email: dto.email,
                password_hash: dto.passwordHash,
            })
            .returning('id')
            .executeTakeFirstOrThrow()
            .then((result) => result.id);
    }

    updateUser(
        db: Transaction,
        userId: number,
        dto: Partial<CreateUserDto>
    ): Promise<boolean> {
        const values = this.getUpdateUserValues(dto);
        return db
            .updateTable('user_ref')
            .set(values)
            .where('id', '=', userId)
            .executeTakeFirst()
            .then((result) => result.numUpdatedRows > 0);
    }

    deleteUserById(db: Transaction, userId: number): Promise<boolean> {
        return db
            .deleteFrom('user_ref')
            .where('id', '=', userId)
            .executeTakeFirst()
            .then((result) => result.numDeletedRows > 0);
    }
}
