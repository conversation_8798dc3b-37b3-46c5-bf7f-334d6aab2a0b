import { Meta, StoryObj } from '@storybook/angular';
import { RadioButtonComponent } from './radio-button.component';

const meta: Meta<RadioButtonComponent> = {
    title: 'Components/Radio Button',
    component: RadioButtonComponent,
    tags: ['autodocs'],
    argTypes: {
        label: {
            control: 'text',
            description: 'Label text for the radio button',
        },
        name: {
            control: 'text',
            description: 'Group name for the radio button',
        },
        checked: {
            control: 'boolean',
            description: 'Whether the radio button is checked',
        },
    },
};

export default meta;
type Story = StoryObj<RadioButtonComponent>;

export const Default: Story = {
    args: {
        label: 'Default Option',
        name: 'defaultGroup',
        checked: false,
    },
};

export const Checked: Story = {
    args: {
        label: 'Checked Option',
        name: 'checkedGroup',
        checked: true,
    },
};

export const GroupExample: Story = {
    render: () => ({
        template: `
      <div style="display: flex; flex-direction: column; gap: 10px;">
        <app-radio-button label="Option 1" name="group1" [checked]="true"></app-radio-button>
        <app-radio-button label="Option 2" name="group1"></app-radio-button>
        <app-radio-button label="Option 3" name="group1"></app-radio-button>
      </div>
    `,
    }),
};
