import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ScreenOneComponent } from '../screen-one/screen-one.component';
import { ScreenTwoComponent } from '../screen-two/screen-two.component';
import { ScreenThreeComponent } from '../screen-three/screen-three.component';

@Component({
    selector: 'app-toggle-buttons',
    standalone: true,
    imports: [
        CommonModule,
        ScreenOneComponent,
        ScreenTwoComponent,
        ScreenThreeComponent,
    ],
    templateUrl: './toggle-buttons.component.html',
    styleUrls: ['./toggle-buttons.component.css'],
})
export class ToggleButtonsComponent {
    currentScreen: string = 'screen-one';

    changeScreen(screen: string): void {
        this.currentScreen = screen;
    }
}
