import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';

@Component({
    selector: 'app-loading-alert',
    templateUrl: './loading-alert.component.html',
    styleUrls: ['./loading-alert.component.css'],
    standalone: true,
    imports: [CommonModule],
})
export class LoadingAlertComponent implements OnInit {
    @Input() message: string = 'Processing...'; // Default message
    @Input() duration: number = 5000; // Default duration (5 seconds)
    @Input() showCloseButton: boolean = true; // Option to manually dismiss
    @Input() backgroundColor: string = '#333'; // Alert background color
    @Input() textColor: string = '#fff'; // Text color

    isVisible: boolean = true;

    ngOnInit(): void {
        if (this.duration > 0) {
            setTimeout(() => {
                this.isVisible = false;
            }, this.duration);
        }
    }

    dismiss(): void {
        this.isVisible = false;
    }
}
