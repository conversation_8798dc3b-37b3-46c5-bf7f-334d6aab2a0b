import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import type { UUID } from 'lib/types/common.no-deps';

@Injectable({ providedIn: 'root' })
export class NavigationService {
    constructor(private readonly router: Router) {}

    routeToAssignmentsTab(page: 'todo' | 'done' = 'todo'): Promise<boolean> {
        return this.router.navigate(['/assignments', page]);
    }

    routeToSearchMainTab(): Promise<boolean> {
        return this.router.navigate(['/search']);
    }

    routeToActionsMainTab(): Promise<boolean> {
        return this.router.navigate(['/actions']);
    }

    routeToNotificationsTab(): Promise<boolean> {
        return this.router.navigate(['/notifications']);
    }

    routeToExecuteTask(reportId: UUID): Promise<boolean> {
        return this.router.navigate(['/execution', reportId]);
    }

    openBottomSheet(
        route: 'new-assignment' = 'new-assignment'
    ): Promise<boolean> {
        return this.router.navigate([
            { outlets: { 'bottom-sheet': `bs/${route}` } },
        ]);
    }

    closeBottomSheet(): Promise<boolean> {
        return this.router.navigate([{ outlets: { 'bottom-sheet': null } }]);
    }
}
