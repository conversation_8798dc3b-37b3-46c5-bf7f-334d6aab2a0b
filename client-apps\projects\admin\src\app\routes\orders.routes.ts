import type { Routes } from '@angular/router';

export const ORDERS_ROUTES: Routes = [
    { path: '', redirectTo: 'pending', pathMatch: 'full' },
    {
        path: '',
        title: 'Orders',
        loadComponent: () =>
            import('../components/dashboards/orders/orders.component').then(
                (m) => m.OrdersComponent
            ),
        children: [
            {
                path: 'pending',
                title: 'Pending',
                loadComponent: () =>
                    import(
                        '../components/dashboards/orders/pending-orders.component'
                    ).then((m) => m.PendingOrdersComponent),
            },
            {
                path: 'assigned',
                title: 'Assigned',
                loadComponent: () =>
                    import(
                        '../components/dashboards/orders/assigned-orders.component'
                    ).then((m) => m.AssignedOrdersComponent),
            },
        ],
    },
];
