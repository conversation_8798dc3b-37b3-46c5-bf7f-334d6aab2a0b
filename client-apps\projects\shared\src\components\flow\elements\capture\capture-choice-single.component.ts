import {
    Component,
    EventEmitter,
    Inject,
    Input,
    OnInit,
    Output,
} from '@angular/core';
import type { Nullable, UUID } from 'lib/types/common.no-deps';
import type { ElementCaptureChoiceSingle } from 'lib/types/flow.no-deps';
import {
    REPORT_EXECUTION_SERVICE_TOKEN,
    ReportExecutionService,
} from '../../../../tokens/report-execution.token';
import { SelectOptionGridComponent } from '../../../select-option-grid/select-option-grid.component';
import { WrapperConditionalElementsComponent } from '../../wrappers/wrapper-conditional-elements.component';

interface Option {
    id: number;
    label: string;
}

@Component({
    selector: 'app-flow-capture-choice-single',
    imports: [SelectOptionGridComponent, WrapperConditionalElementsComponent],
    templateUrl: './capture-choice-single.component.html',
})
export class FlowCaptureChoiceSingleComponent implements OnInit {
    @Input({ required: true }) element!: ElementCaptureChoiceSingle;
    @Input({ required: true }) elementPath!: UUID[];
    @Input({ required: true }) indent!: number;

    @Output() valueChanged: EventEmitter<Nullable<number[]>> =
        new EventEmitter();

    options!: Option[];

    selectedOptions: number[] = [];

    constructor(
        @Inject(REPORT_EXECUTION_SERVICE_TOKEN)
        private readonly reportExecutionService: ReportExecutionService
    ) {}

    ngOnInit(): void {
        this.reportExecutionService
            .getAnswerValue(this.elementPath)
            .subscribe((answerValue) => {
                if (answerValue.type !== 'numberArray') {
                    throw new Error(
                        `Expected answer value to be of type "numberArray": ${answerValue.type}`
                    );
                }

                this.selectedOptions = answerValue.value
                    ? [answerValue.value[0]]
                    : [];
            });

        this.options = this.element.options.map((option, i) => ({
            id: i,
            label: option,
        }));
    }

    selectOption(id: number): void {
        this.valueChanged.emit([id]);
    }
}
