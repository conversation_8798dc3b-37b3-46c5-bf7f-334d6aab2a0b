import {
    Component,
    EventEmitter,
    Inject,
    Input,
    OnInit,
    Output,
} from '@angular/core';
import type { Nullable, UUID } from 'lib/types/common.no-deps';
import type { ElementCaptureChoiceMultiple } from 'lib/types/flow.no-deps';
import {
    REPORT_EXECUTION_SERVICE_TOKEN,
    ReportExecutionService,
} from '../../../../tokens/report-execution.token';
import { SelectOptionGridComponent } from '../../../select-option-grid/select-option-grid.component';
import { WrapperConditionalElementsComponent } from '../../wrappers/wrapper-conditional-elements.component';

interface Option {
    id: number;
    label: string;
}

@Component({
    selector: 'app-flow-capture-choice-multiple',
    imports: [SelectOptionGridComponent, WrapperConditionalElementsComponent],
    templateUrl: './capture-choice-multiple.component.html',
})
export class FlowCaptureMultipleChoiceComponent implements OnInit {
    @Input({ required: true }) element!: ElementCaptureChoiceMultiple;
    @Input({ required: true }) elementPath!: UUID[];
    @Input({ required: true }) indent!: number;

    @Output() valueChanged: EventEmitter<Nullable<number[]>> =
        new EventEmitter();

    options!: Option[];

    selectedOptions: number[] = [];

    constructor(
        @Inject(REPORT_EXECUTION_SERVICE_TOKEN)
        private readonly reportExecutionService: ReportExecutionService
    ) {}

    ngOnInit(): void {
        this.reportExecutionService
            .getAnswerValue(this.elementPath)
            .subscribe((answerValue) => {
                if (answerValue.type !== 'numberArray') {
                    throw new Error(
                        `Expected answer value to be of type "numberArray": ${answerValue.type}`
                    );
                }

                this.selectedOptions = answerValue.value
                    ? answerValue.value
                    : [];
            });

        this.options = this.element.options.map((option, i) => ({
            id: i,
            label: option,
        }));
    }

    toggleOption(id: number): void {
        // Create a new selection based on current selection.
        const nextSelection = new Set(this.selectedOptions);

        if (nextSelection.has(id)) {
            nextSelection.delete(id);
        } else {
            nextSelection.add(id);
        }

        this.valueChanged.emit(Array.from(nextSelection));
    }
}
