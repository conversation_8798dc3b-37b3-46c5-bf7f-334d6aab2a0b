import type { Optional, UUID } from 'lib/types/common.no-deps';
import type { Order } from 'lib/types/order.no-deps';
import type { Transaction } from '../../../database/database.types';

export interface CreateOneDTO
    extends Omit<Order, 'createdAt' | 'id' | 'status'> {
    status?: Order['status'];
}

export abstract class AbstractOrderRepository {
    abstract fetchAll(db: Transaction): Promise<Order[]>;

    abstract fetchById(db: Transaction, id: UUID): Promise<Optional<Order>>;

    abstract createOne(db: Transaction, dto: CreateOneDTO): Promise<UUID>;

    abstract deleteOne(db: Transaction, id: UUID): Promise<boolean>;
}
