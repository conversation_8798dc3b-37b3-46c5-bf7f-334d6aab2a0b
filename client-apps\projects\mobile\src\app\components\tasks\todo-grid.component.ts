import { CommonModule } from '@angular/common';
import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { LoadingComponent, TaskCardComponent } from '@shared/public-api';
import { GridLayoutComponent } from '@shared/components/layouts/grid-layout.component';
import type { UUID } from 'lib/types/common.no-deps';
import type { TaskAssignment, TaskReport } from 'lib/types/task.no-deps';
import type { Subscription } from 'rxjs';
import { NavigationService } from '../../core/services/navigation.service';
import { AssignmentService } from '../../domain/tasks/assignments/assignment.service';
import { ReportService } from '../../domain/tasks/reports/report.service';

@Component({
    selector: 'app-todo-grid',
    imports: [
        CommonModule,
        LoadingComponent,
        GridLayoutComponent,
        TaskCardComponent,
    ],
    templateUrl: './todo-grid.component.html',
    styles: [
        `
            :host {
                display: flex;
                flex-grow: 1;
            }
        `,
    ],
})
export class TodoGridComponent implements OnD<PERSON>roy, OnInit {
    assignments: Map<UUID, TaskAssignment> = new Map();

    reports: Map<UUID, TaskReport> = new Map();

    assignmentTags = [{ text: '#25' }, { text: 'Defect' }];
    reportTags = [{ text: 'Leadership' }];

    isPreparingReport = false;

    private subscriptions: Subscription[] = [];

    constructor(
        private readonly assignmentService: AssignmentService,
        private readonly reportService: ReportService,
        private readonly routeService: NavigationService
    ) {}

    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions.
        this.subscriptions.forEach((subscription) =>
            subscription.unsubscribe()
        );
    }

    ngOnInit(): void {
        this.subscribeToAssignments();
        this.subscribeToReports();
    }

    async onClickAssignment(assignment: TaskAssignment): Promise<void> {
        // Show report is preparing indicator.
        this.isPreparingReport = true;
        // Create the report.
        const report = await this.reportService.createForAssignment(assignment);
        // Hide report is preparing indicator.
        this.isPreparingReport = false;
        // Route to report execution page.
        this.routeService.routeToExecuteTask(report.id);
    }

    onClickReport(reportId: UUID): void {
        this.routeService.routeToExecuteTask(reportId);
    }

    private subscribeToAssignments = (): void => {
        const subscription = this.assignmentService
            .getNotStarted()
            .subscribe((assignments) => {
                assignments.forEach((assignment) => {
                    this.assignments.set(assignment.id, assignment);
                });
            });
        this.subscriptions.push(subscription);
        this.assignmentService.fetchAll();
    };

    private subscribeToReports = (): void => {
        const subscription = this.reportService
            .getInProgress()
            .subscribe((reports) => {
                reports.forEach((report) => {
                    this.reports.set(report.id, report);
                });
            });
        this.subscriptions.push(subscription);
        this.reportService.fetchAll();
    };
}
