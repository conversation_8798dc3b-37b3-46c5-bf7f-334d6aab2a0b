<!-- sidebar-layout.component.html -->
<div class="flex bg-gray-100 text-gray-800 h-full">
    <!-- Left Drawer -->
    <div
        class="flex flex-col items-center shadow-lg justify-between min-w-24 transition-all duration-300 bg-gray-200 overflow-hidden ease-in-out"
        [ngClass]="isDrawerOpen ? 'w-48' : 'w-24'"
    >
        <div class="mt-4 p-4">
            <img src="/shared/logos/opex6.png" />
        </div>

        <!-- Navigation Menu -->
        <nav class="mt-4">
            <ul>
                <li *ngFor="let item of drawerMenuItems" class="mb-2">
                    <a 
                        (click)="item.action()" 
                        class="flex items-center px-4 py-2 hover:bg-gray-300 cursor-pointer"
                    >
                        <!-- Icon -->
                        <img [src]="`/icons/${item.icon}.svg`" class="h-6 w-6" />

                        <!-- Label -->
                        <span class="ml-3" [ngClass]="isDrawerOpen ? 'block' : 'hidden'">{{ item.label }}</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Drawer Toggle -->
        <div class="p-4">
            <button 
                class="flex items-center px-4 py-2 hover:bg-gray-300 cursor-pointer"
                (click)="toggleDrawer()"
            >
                <img class="h-6 w-6" src="/icons/hamburger.svg" />
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex items-center justify-center w-full h-full p-4">
        <div class="bg-white rounded-lg shadow-lg w-full h-full overflow-hidden flex flex-col">
            <!-- Header -->
            <header class="bg-white shadow-lg">
                <div class="px-4 py-3">
                    <div class="flex items-center justify-between">
                        <!-- Left side - Growing Title -->
                        <div class="flex mr-6">
                            <h1 class="text-xl md:text-2xl font-bold truncate">{{ title }}</h1>
                        </div>

                        <!-- Right side - Menu Items -->
                        <div class="flex items-center space-x-4">
                            <!-- Settings Button -->
                            <button class="p-2 rounded-full hover:bg-gray-100" title="Settings">
                                <i class="fas fa-cog"></i>
                            </button>

                            <!-- New Action Button -->
                            <button class="p-2 bg-sky-600 text-white hover:bg-sky-800 rounded-full flex">
                                <app-icon-plus class="standard-icon-size w-4 text-white"></app-icon-plus>
                            </button>

                            <!-- Separator -->
                            <div class="h-6 w-px bg-gray-300"></div>

                            <!-- Account Dropdown -->                            
                            <app-dropdown-menu menuPosition="right">
                                    <ng-template #menuButton let-isOpen>
                                    <button
                                        [class.bg-gray-200]="isOpen"
                                        class="flex items-center hover:bg-gray-200 space-x-2 px-4 py-2 transition-all"
                                    >
                                        <!-- User Icon -->
                                        <div class="w-8 h-8 rounded-full bg-sky-600 flex items-center justify-center text-white">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <!-- Username -->
                                        <span class="font-medium">John Doe</span>
                                        <!-- Dropdown Arrow -->
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </ng-template>
                                <div menuContent class="w-48">
                                    <app-dropdown-menu-item>
                                        <a href="#" class="flex items-center p-4 hover:bg-gray-100">
                                            <img class="h-5 w-5 mr-2" src="/icons/cog.svg" />
                                            Settings
                                        </a>
                                    </app-dropdown-menu-item>
                                    <div class="border-t border-gray-100 my-1"></div>
                                    <app-dropdown-menu-item>
                                        <a href="#" class="flex items-center p-4 hover:bg-gray-100">
                                            <app-icon-exit class="h-5 w-5 mr-2 text-red-500"></app-icon-exit>
                                            Log out
                                        </a>
                                    </app-dropdown-menu-item>
                                </div>
                            </app-dropdown-menu>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Scrollable Content -->
            <div class="flex flex-grow overflow-y-auto p-4">
                <ng-content></ng-content>
            </div>
        </div>
    </div>
</div>