export class ConstSet<const T extends ReadonlyArray<unknown>> {
    private readonly set: Set<T[number]>;

    constructor(values: T) {
        this.set = new Set(values);
    }

    assertAndReturn<U extends unknown>(value: U): Extract<U, T[number]> {
        this.assert(value); // Will throw if invalid
        return value as Extract<U, T[number]>;
    }

    assert(value: unknown): asserts value is T[number] {
        if (!this.has(value)) {
            throw new Error(
                `Value must be one of: ${Array.from(this.set.values()).join(', ')}. Received: ${value}`
            );
        }
    }

    has(value: unknown): value is T[number] {
        return this.set.has(value as T[number]);
    }
}
