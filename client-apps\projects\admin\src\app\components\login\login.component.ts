// src/app/login/login.component.ts
import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
    ReactiveFormsModule,
    FormBuilder,
    FormGroup,
    Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { GoogleSignInComponent } from '../google-sign-in/google-sign-in.component';

@Component({
    standalone: true,
    selector: 'app-login',
    imports: [CommonModule, ReactiveFormsModule, GoogleSignInComponent],
    templateUrl: './login.component.html',
    styleUrls: ['./login.component.css'],
})
export class LoginComponent {
    loginForm: FormGroup;

    constructor(
        private fb: FormBuilder,
        private router: Router
    ) {
        this.loginForm = this.fb.group({
            email: ['', [Validators.required, Validators.email]],
            password: ['', [Validators.required]],
        });
    }

    onLogin(): void {
        if (this.loginForm.valid) {
            // Perform your login logic here
            console.log('Login Form Data:', this.loginForm.value);
        }
    }

    onForgotPassword(): void {
        this.router.navigate(['/forgot-password']);
    }

    onSignUp(): void {
        this.router.navigate(['/sign-up']);
    }
}
