import { Injectable } from '@angular/core';
import type { ChangeSet, Optional, UUID } from 'lib/types/common.no-deps';
import type {
    TaskAssignment,
    TaskReport,
    TaskReportStatus,
} from 'lib/types/task.no-deps';
import { isEmptyChangeSet } from 'lib/util/change-set';
import { generateUUID } from 'lib/util/uuid';
import { BehaviorSubject, map, Observable } from 'rxjs';
import { Transactional } from '../../../core/decorators/transactional.decorator';
import { UserService } from '../../users/user.service';
import { AssignmentService } from '../assignments/assignment.service';
import { ReportRepository } from './report.repository';

interface ReportChangeSet extends ChangeSet<TaskReport, TaskReport> {}

@Injectable({ providedIn: 'root' })
export class ReportService {
    private reportsSubject = new BehaviorSubject<TaskReport[]>([]);

    reports$ = this.reportsSubject.asObservable();

    constructor(
        private readonly assignmentService: AssignmentService,
        private readonly reportRepository: ReportRepository,
        private readonly userService: UserService
    ) {}

    fetchAll(): Promise<void> {
        return this.loadAllReports();
    }

    fetchById(id: UUID): Promise<Optional<TaskReport>> {
        return this.reportRepository.fetchById(id);
    }

    fetchByStatus(status: TaskReportStatus): Promise<TaskReport[]> {
        return this.reportRepository.fetchByStatus(status);
    }

    getInProgress(): Observable<TaskReport[]> {
        return this.reports$.pipe(
            map((reports) =>
                reports.filter((report) => report.status === 'reportInProgress')
            )
        );
    }

    getComplete(): Observable<TaskReport[]> {
        return this.reports$.pipe(
            map((reports) =>
                reports.filter(
                    (report) =>
                        report.status === 'reportCompleted' ||
                        report.status === 'reportApproved'
                )
            )
        );
    }

    @Transactional()
    async createAdHocReport(templateId: UUID): Promise<TaskReport> {
        // Create an ad-hoc assignment.
        const assignment =
            await this.assignmentService.createAssignmentForTemplate(
                templateId
            );

        // Create the report itself beaed on the ad-hoc assignemnt.
        const report = await this.createForAssignment(assignment);

        return report;
    }

    @Transactional()
    async createForAssignment(assignment: TaskAssignment): Promise<TaskReport> {
        const now = new Date().toISOString();

        const currentUserId = await this.userService.getCurrentUserId();
        const report: TaskReport = {
            answers: {},
            assignmentId: assignment.id,
            createdAt: now,
            createdBy: currentUserId,
            id: generateUUID(),
            isDeleted: false,
            label: assignment.label,
            lastModifiedAt: now,
            lastModifiedBy: currentUserId,
            metadata: {},
            status: 'reportInProgress',
        };

        await Promise.all([
            // Create report
            this.reportRepository.createBulk([report]),
            // Mark assignment as `inProgress`.
            this.assignmentService.updateStatus(
                assignment,
                'assignmentInProgress'
            ),
        ]);

        return report;
    }

    @Transactional()
    async markAsComplete(id: UUID): Promise<void> {
        const report = (await this.reportRepository.fetchById(id)).orElseThrow(
            () => new Error(`Expected report to exist: ${id}`)
        );

        const [updatedReportId] = await this.reportRepository.updateBulk([
            {
                ...report,
                lastModifiedAt: new Date().toISOString(),
                status: 'reportCompleted',
            },
        ]);
        const updatedReportOpt =
            await this.reportRepository.fetchById(updatedReportId);
        const updatedReport = updatedReportOpt.orElseThrow(
            () => new Error(`Updated report not found: ${updatedReportId}`)
        );

        this.reportsSubject.next([updatedReport]);
    }

    @Transactional()
    async upsertReports(incomingReports: TaskReport[]): Promise<void> {
        if (incomingReports.length === 0) {
            return;
        }

        const changeSet = await this.createChangeSet(incomingReports);
        const savedIds = await this.saveChangeSet(changeSet);

        if (savedIds.length > 0) {
            this.loadReportsByIds(savedIds);
        }
    }

    private createChangeSet = async (
        incomingReports: TaskReport[]
    ): Promise<ReportChangeSet> => {
        // Fetch corresponding existing reports for incoming reports.
        const incomingIds = incomingReports.map((report) => report.id);
        const existingReports =
            await this.reportRepository.fetchByIds(incomingIds);

        // Turn existing reports into a Map for faster lookup.
        const existingReportsMap = new Map(
            existingReports.map((report) => [report.id, report])
        );

        return incomingReports.reduce<ReportChangeSet>(
            (acc, incomingReport) => {
                // Find corresponding existing report.
                const existingReport = existingReportsMap.get(
                    incomingReport.id
                );

                // If no corresponding existing report - it is a new report.
                if (!existingReport) {
                    acc.toCreate.push(incomingReport);
                    return acc;
                }

                // Compare incoming report with existing report based on `lastModifiedAt`
                // to determine if it's newer. If it is, then we want to update it.
                if (
                    new Date(incomingReport.lastModifiedAt) >
                    new Date(existingReport.lastModifiedAt)
                ) {
                    acc.toUpdate.push(incomingReport);
                }

                return acc;
            },
            { toCreate: [], toUpdate: [] }
        );
    };

    private saveChangeSet = async (
        changeSet: ReportChangeSet
    ): Promise<UUID[]> => {
        if (isEmptyChangeSet(changeSet)) {
            return [];
        }

        const [createdIds, updatedIds] = await Promise.all([
            this.reportRepository.createBulk(changeSet.toCreate),
            this.reportRepository.updateBulk(changeSet.toUpdate),
        ]);

        return [...createdIds, ...updatedIds];
    };

    private loadAllReports = async (): Promise<void> => {
        const reports = await this.reportRepository.fetchAll();

        this.reportsSubject.next(reports);
    };

    private loadReportsByIds = async (ids: UUID[]): Promise<void> => {
        if (ids.length === 0) {
            return;
        }

        const reports = await this.reportRepository.fetchByIds(ids);

        this.reportsSubject.next(reports);
    };
}
