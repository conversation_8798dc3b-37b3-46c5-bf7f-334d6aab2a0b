import { Service } from 'diod';
import { type UUID, Optional } from 'lib/types/common.no-deps';
import type { Message } from 'lib/types/message.no-deps';
import type {
    Order,
    OrderChangeDTO,
    OrdersFetchDTO,
} from 'lib/types/order.no-deps';
import type { Transaction } from '../../database/database.types';
import { withTransaction } from '../../database/database.util';
import { WsPublisher } from '../../shared/ws-publisher';
import { AbstractOrderRepository } from './abstract/abstract-repository';
import {
    AbstractOrderService,
    type CreateOneDTO,
    type OrderEvent,
} from './abstract/abstract-service';

@Service()
export class OrderService implements AbstractOrderService {
    constructor(
        private readonly clientPublisher: WsPublisher,
        private readonly orderRepository: AbstractOrderRepository
    ) {}

    getChannel(): string {
        return 'orders';
    }

    onSub(pubFn: (message: Message<OrdersFetchDTO, OrderEvent>) => void): void {
        withTransaction(async (tx) => {
            const orders = await this.fetchAll(tx);

            pubFn({ payload: { orders }, type: 'fetchedAllOrders' });
        });
    }

    fetchAll(db: Transaction): Promise<Order[]> {
        return this.orderRepository.fetchAll(db);
    }

    fetchById(db: Transaction, id: UUID): Promise<Optional<Order>> {
        return this.orderRepository.fetchById(db, id);
    }

    async createOne(
        db: Transaction,
        dto: CreateOneDTO
    ): Promise<Optional<UUID>> {
        const orderId = await this.orderRepository.createOne(db, dto);
        const orderOpt = await this.orderRepository.fetchById(db, orderId);

        if (!orderOpt.isPresent()) {
            console.error('Could not find newly created order:', orderId);
            return Optional.empty();
        }

        this.publishToClients(orderOpt.get(), 'orderCreated');

        return Optional.of(orderId);
    }

    async deleteOne(db: Transaction, id: UUID): Promise<boolean> {
        const success = await this.orderRepository.deleteOne(db, id);

        if (success) {
            this.publishToClients<OrderChangeDTO>(
                {
                    orderId: id,
                },
                'orderDeleted'
            );
        }

        return success;
    }

    private publishToClients = <P>(payload: P, type: OrderEvent): void => {
        this.clientPublisher.pub('orders', { payload, type });
    };
}
