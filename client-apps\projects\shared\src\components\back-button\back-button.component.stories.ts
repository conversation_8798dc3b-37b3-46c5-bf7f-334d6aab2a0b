import { Meta, StoryObj } from '@storybook/angular';
import { BackButtonComponent } from './back-button.component';

const meta: Meta<BackButtonComponent> = {
    title: 'Components/BackButton',
    component: BackButtonComponent,
    tags: ['autodocs'],
    render: (args: BackButtonComponent) => ({
        props: {
            ...args,
        },
    }),
};

export default meta;
type Story = StoryObj<BackButtonComponent>;

export const Default: Story = {
    args: {},
};

export const WithCustomClick: Story = {
    args: {},
    render: (args) => ({
        props: {
            ...args,
            onNavigateBack: () => alert('Custom back action'),
        },
    }),
};
