import { Service } from 'diod';
import type { Message } from 'lib/types/message.no-deps';
import type { Server, Socket } from 'socket.io';

export interface WsChannelSubObserver {
    getChannel(): string;

    onSub(pubFn: <P, T extends string>(message: Message<P, T>) => void): void;
}

/**
 * Utility that is meant to be used by used by services to send messages to
 * web socket clients.
 */
export abstract class WsPublisher {
    // Connects the publisher to the WebSocket Server.
    abstract connect(wsServer: Server): void;

    // Publishes a message to all sockets subscribed to a channel.
    abstract pub<P, T extends string>(
        channel: string,
        message: Message<P, T>
    ): void;

    // Handler for new socket subscriptions.
    abstract onSub(socket: Socket, channel: string): void;

    // Registers observers for new subscriptions to a channel.
    abstract addChannelSubObserver(observer: WsChannelSubObserver): void;

    // Removes registered observers for new subscriptions to a channel.
    abstract removeChannelSubObserver(observer: WsChannelSubObserver): void;
}

/**
 * Utility that is meant to be used by used by services to send messages to
 * web socket clients.
 */
@Service()
export class WsPublisherImpl implements WsPublisher {
    private subscriptionObservers = new Map<
        string,
        Set<WsChannelSubObserver>
    >();
    private wsServer?: Server;

    connect(server: Server): void {
        this.wsServer = server;
    }

    pub<P, T extends string>(channel: string, message: Message<P, T>): void {
        if (!this.wsServer) {
            console.error(`WebSocket server not connected!`);
            return;
        }
        this.wsServer.to(channel).emit(message.type, message.payload);
    }

    onSub(socket: Socket, channel: string): void {
        // Loop through the observers for given channel and call their method.
        this.subscriptionObservers
            .get(channel)
            ?.forEach((observer) =>
                observer.onSub((msg) => this.pubToSocket(socket, msg))
            );
    }

    addChannelSubObserver(observer: WsChannelSubObserver): void {
        const channel = observer.getChannel();
        // Ensure a set exists for the observer's channel.
        let channelSet = this.subscriptionObservers.get(channel);
        if (!channelSet) {
            channelSet = new Set();
            this.subscriptionObservers.set(channel, channelSet);
        }
        channelSet.add(observer);
    }

    removeChannelSubObserver(observer: WsChannelSubObserver): void {
        const channelSet = this.subscriptionObservers.get(
            observer.getChannel()
        );

        if (!channelSet) {
            console.error(
                `Cannot remove observer from a channel he did not subscribe to:`,
                observer
            );
            return;
        }

        if (!channelSet.delete(observer)) {
            console.error(
                `Observer was not registered to the channel:`,
                observer
            );
        }
    }

    private pubToSocket = <P, T extends string>(
        socket: Socket,
        message: Message<P, T>
    ): void => {
        socket.emit(message.type, message.payload);
    };
}
