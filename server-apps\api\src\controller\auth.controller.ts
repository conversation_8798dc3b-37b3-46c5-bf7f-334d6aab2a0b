import bcrypt from 'bcrypt';
import { OAuth2Client } from 'google-auth-library';
import jwt, { JwtPayload } from 'jsonwebtoken';
import { generateUUID } from 'lib/util/uuid';
import { Body, Controller, Post, Request, Route, Tags } from 'tsoa';
import { verifyAzureAdToken } from '../azure.helper';
import { container } from '../diod.config';
import { AbstractUserRepository } from '../domain/user/abstract/user.repository';
import type { ApiRequest } from '../shared/request.type';

const googleClient = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);
const JWT_SECRET = process.env.JWT_SECRET || 'dev_secret_key'; // Replace in production

// Bodies
interface LoginBody {
    email: string;
    password: string;
}

interface SignupBody extends LoginBody {
    userName: string;
}

interface IdProviderBody {
    idToken: string;
}

// Responses
interface ErrorResponse {
    message?: string;
}

interface SignupResponse extends ErrorResponse {
    token?: string;
    user?: {
        id: number;
        shareId: string;
        email: string;
        userName: string;
    };
}

interface LoginResponse extends ErrorResponse {
    token?: string;
    user?: {
        id: number;
        shareId: string;
        email: string;
    };
}

interface IdProviderResponse extends ErrorResponse {
    token?: string;
}

@Route('auth')
@Tags('auth')
export class AuthController extends Controller {
    private readonly userRepository: AbstractUserRepository;

    constructor() {
        super();
        this.userRepository = container.get(AbstractUserRepository);
    }

    @Post('signup')
    async signup(
        @Request() request: ApiRequest,
        @Body() body: SignupBody
    ): Promise<SignupResponse> {
        try {
            // Validate body
            const { email, password, userName } = body;
            if (!email || !password || !userName) {
                this.setStatus(400);
                return { message: 'Email, password, and userName required.' };
            }

            const db = request.transaction;

            // Check to see if user already exists (by email)
            const existingUserOpt = await this.userRepository.fetchUserByEmail(
                db,
                email
            );
            if (existingUserOpt.isPresent()) {
                this.setStatus(409);
                return { message: 'User with this email already exists.' };
            }

            // Create password hash then createa user
            const passwordHash = await bcrypt.hash(password, 10);
            const shareId = generateUUID();
            const userId = await this.userRepository.createUser(db, {
                shareId,
                userName,
                email,
                passwordHash,
            });

            // Create JWT token for user
            const token = jwt.sign({ userId, email }, JWT_SECRET, {
                expiresIn: '8h',
            });

            // Return user details
            this.setStatus(201);
            return {
                token,
                user: {
                    id: userId,
                    shareId,
                    email,
                    userName,
                },
            };
        } catch (err) {
            console.error('Signup error:', err);
            this.setStatus(500);
            return { message: 'Internal server error.' };
        }
    }

    @Post('login')
    async login(
        @Request() request: ApiRequest,
        @Body() body: LoginBody
    ): Promise<LoginResponse> {
        try {
            // Validate body
            const { email, password } = body;
            if (!email || !password) {
                this.setStatus(400);
                return { message: 'Email and password required.' };
            }

            const db = request.transaction;

            // Try to fetch existing user
            const userOpt = await this.userRepository.fetchUserByEmail(
                db,
                email
            );
            if (!userOpt.isPresent()) {
                this.setStatus(401);
                return { message: 'Invalid email or password.' };
            }
            const user = userOpt.get();

            // Validate password
            const valid = await bcrypt.compare(password, user.passwordHash);
            if (!valid) {
                this.setStatus(401);
                return { message: 'Invalid email or password.' };
            }

            // Create JWT token for user
            const token = jwt.sign(
                { userId: user.id, email: user.email },
                JWT_SECRET,
                { expiresIn: '8h' }
            );

            // Return user details
            return {
                token,
                user: {
                    id: user.id,
                    shareId: user.shareId,
                    email: user.email,
                },
            };
        } catch (err) {
            console.error('Login error:', err);
            this.setStatus(500);
            return { message: 'Internal server error.' };
        }
    }

    @Post('google')
    async google(
        @Request() request: ApiRequest,
        @Body() body: IdProviderBody
    ): Promise<IdProviderResponse> {
        try {
            // Validate body
            const { idToken } = body;
            if (!idToken) {
                this.setStatus(400);
                return { message: 'Google ID token required.' };
            }

            // Verify ID token
            const ticket = await googleClient.verifyIdToken({
                idToken,
                audience: process.env.GOOGLE_CLIENT_ID,
            });
            const payload = ticket.getPayload();
            if (!payload || !payload.email) {
                console.error('Invalid Google payload or email');
                this.setStatus(401);
                return { message: 'Invalid Google token.' };
            }

            const db = request.transaction;

            // Create an user entry if one does not already exist
            let userOpt = await this.userRepository.fetchUserByEmail(
                db,
                payload.email
            );
            if (!userOpt.isPresent()) {
                const shareId =
                    typeof crypto !== 'undefined' && crypto.randomUUID
                        ? crypto.randomUUID()
                        : Math.random().toString(36).substring(2, 15);
                const userId = await this.userRepository.createUser(db, {
                    shareId,
                    userName: payload.name || payload.email,
                    email: payload.email,
                    passwordHash: '', // No password for Google users
                });
                userOpt = await this.userRepository.fetchUserById(db, userId);
            }
            const user = userOpt.get();

            // Create JWT token for user and return it
            const token = jwt.sign(
                { userId: user.id, email: user.email },
                JWT_SECRET,
                { expiresIn: '8h' }
            );
            return { token };
        } catch (err) {
            console.error('Google login error:', err);
            this.setStatus(500);
            return { message: 'Internal server error.' };
        }
    }

    @Post('azure')
    async azure(
        @Request() request: ApiRequest,
        @Body() body: IdProviderBody
    ): Promise<IdProviderResponse> {
        try {
            // Validate body
            const { idToken } = body;
            if (!idToken) {
                this.setStatus(400);
                return { message: 'Azure AD ID token required.' };
            }

            // Verify ID token
            let payload: JwtPayload;
            try {
                payload = await verifyAzureAdToken(idToken);
            } catch (err) {
                console.error('Azure AD token verification error:', err);
                this.setStatus(401);
                return { message: 'Invalid Azure AD token.' };
            }
            if (!payload || !payload.email) {
                console.error('Invalid Azure payload or email');
                this.setStatus(401);
                return { message: 'Invalid Azure AD token.' };
            }

            const db = request.transaction;

            // Create an user entry if one does not already exist
            let userOpt = await this.userRepository.fetchUserByEmail(
                db,
                payload.email
            );
            if (!userOpt.isPresent()) {
                const shareId =
                    typeof crypto !== 'undefined' && crypto.randomUUID
                        ? crypto.randomUUID()
                        : Math.random().toString(36).substring(2, 15);
                await this.userRepository.createUser(db, {
                    shareId,
                    userName: payload.name || payload.email,
                    email: payload.email,
                    passwordHash: '', // No password for Azure AD users
                });
                userOpt = await this.userRepository.fetchUserByEmail(
                    db,
                    payload.email
                );
            }
            const user = userOpt.get();

            // Create JWT token for user and return it
            const token = jwt.sign(
                { userId: user.id, email: user.email },
                JWT_SECRET,
                { expiresIn: '1d' }
            );
            return { token };
        } catch (err) {
            console.error('Azure AD login error:', err);
            this.setStatus(500);
            return { message: 'Internal server error.' };
        }
    }
}
