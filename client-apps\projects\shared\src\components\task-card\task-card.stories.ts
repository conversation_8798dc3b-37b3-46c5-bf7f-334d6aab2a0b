import type { Meta, StoryObj } from '@storybook/angular';
import { TaskCardComponent } from './task-card.component';

const meta: Meta<TaskCardComponent> = {
    title: 'Components/TaskCard',
    component: TaskCardComponent,
    tags: ['autodocs'],
    render: (args) => ({
        props: args,
    }),
};

export default meta;
type Story = StoryObj<TaskCardComponent>;

export const Default: Story = {
    args: {
        task: {
            title: 'Leadership',
            dueDate: '19 March 2024',
            categories: ['Leadership', 'Maintenance'],
            reportProcedure: 'Shift Changeover',
            reportSubject: 'Packaging',
        },
    },
};

export const SingleCategory: Story = {
    args: {
        task: {
            title: 'Safety Check',
            dueDate: '20 March 2024',
            categories: ['Safety'],
            reportProcedure: 'Daily Check',
            reportSubject: 'Equipment',
        },
    },
};

export const LongTitle: Story = {
    args: {
        task: {
            title: 'Comprehensive Manufacturing Process Review and Documentation Update',
            dueDate: '21 March 2024',
            categories: ['Documentation', 'Process'],
            reportProcedure: 'Monthly Review',
            reportSubject: 'Manufacturing',
        },
    },
};
