import type { TaskAssignment, TaskReport } from 'lib/types/task.no-deps';
import type { Transaction } from '../../../../database/database.types';

interface SyncDTO {
    assignments: TaskAssignment[];
    reports: TaskReport[];
}

// Bodies
export interface SyncRequestDTO extends SyncDTO {
    lastSyncTime?: string; // ISO 8601 date-time string
}

// Responses
export interface SyncResponseDTO extends SyncDTO {
    requestTime: string; // ISO 8601 date-time string
}

export abstract class AbstractTaskReportService {
    abstract sync(
        db: Transaction,
        dto: SyncRequestDTO
    ): Promise<SyncResponseDTO>;
}
