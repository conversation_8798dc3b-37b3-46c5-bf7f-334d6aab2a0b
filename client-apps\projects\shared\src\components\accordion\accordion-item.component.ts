// accordion-item.component.ts
import { Component, Input, signal } from '@angular/core';

@Component({
    selector: 'app-accordion-item',
    standalone: true,
    template: `
        <div class="border border-gray-200 rounded-lg overflow-hidden mb-2">
            <button
                class="w-full p-4 text-left bg-gray-50 hover:bg-gray-100 transition-colors duration-200 flex justify-between items-center"
                (click)="isExpanded.set(!isExpanded())"
            >
                <span class="font-medium">{{ title }}</span>
                <svg
                    class="w-5 h-5 transition-transform duration-200"
                    [class.transform.rotate-180]="isExpanded()"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 9l-7 7-7-7"
                    />
                </svg>
            </button>
            <div class="p-4 bg-white" [class.hidden]="!isExpanded()">
                <ng-content></ng-content>
            </div>
        </div>
    `,
})
export class AccordionItemComponent {
    @Input() title = '';
    isExpanded = signal(false);
}
