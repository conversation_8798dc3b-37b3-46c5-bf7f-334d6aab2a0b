.task-card {
  background: #fff;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  max-width: 400px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.task-title h3 {
  margin: 0;
  font-size: 18px;
}

.task-title p {
  margin: 0;
  font-size: 12px;
  color: gray;
}

.task-icon {
  font-size: 24px;
}

.start-action {
  background: blue;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 5px;
  cursor: pointer;
}

.start-action:hover {
  background: darkblue;
}

.task-tags {
  margin-top: 10px;
}

.tag {
  background: #e0e0e0;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  margin-right: 5px;
}

.task-info {
  margin-top: 10px;
}

label {
  font-size: 12px;
  display: block;
  margin-top: 10px;
}

input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 5px;
  background: #f5f5f5;
}