import type { Meta, StoryObj } from '@storybook/angular';
import { ShiftChangeoverComponent } from './shift-changeover.component';

const meta: Meta<ShiftChangeoverComponent> = {
    title: 'Components/ShiftChangeover',
    component: ShiftChangeoverComponent,
    tags: ['autodocs'],
    parameters: {
        layout: 'centered',
    },
};

export default meta;
type Story = StoryObj<ShiftChangeoverComponent>;

export const Default: Story = {
    args: {},
};

export const WithCustomTitle: Story = {
    args: {},
    render: (args) => ({
        props: args,
        template: `
      <div style="width: 600px">
        <app-shift-changeover></app-shift-changeover>
      </div>
    `,
    }),
};

export const Mobile: Story = {
    args: {},
    parameters: {
        viewport: {
            defaultViewport: 'mobile1',
        },
    },
    render: (args) => ({
        props: args,
        template: `
      <div style="width: 320px">
        <app-shift-changeover></app-shift-changeover>
      </div>
    `,
    }),
};

export const Tablet: Story = {
    args: {},
    parameters: {
        viewport: {
            defaultViewport: 'tablet',
        },
    },
    render: (args) => ({
        props: args,
        template: `
      <div style="width: 768px">
        <app-shift-changeover></app-shift-changeover>
      </div>
    `,
    }),
};
