<div class="login-container">
  <h2>Welcome back!</h2>

  <form [formGroup]="loginForm" (ngSubmit)="onLogin()">
    <label>Email Address</label>
    <input formControlName="email" type="email" placeholder="Enter your email" />
    <div *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched">
      Please enter a valid email.
    </div>

    <label>Password</label>
    <input formControlName="password" type="password" placeholder="Enter your password" />
    <div *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">
      Password is required.
    </div>

    <button type="submit" [disabled]="loginForm.invalid">Login</button>
  </form>

  <div class="links">
    <a>Forgot Password? <span (click)="onForgotPassword()">Recover</span></a>
  </div>

  <!-- Include the standalone Google Sign-In component -->
  <app-google-sign-in></app-google-sign-in>

  <p>
    Don't have an account?
    <a (click)="onSignUp()" class="signup">Sign Up</a>
  </p>
</div>