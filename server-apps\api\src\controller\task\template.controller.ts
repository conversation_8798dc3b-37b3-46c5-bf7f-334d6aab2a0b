import type { TaskTemplate, TaskTemplateStatus } from 'lib/types/task.no-deps';
import type { UUID } from 'lib/types/common.no-deps';
import {
    Body,
    Controller,
    Delete,
    Get,
    Path,
    Post,
    Put,
    Query,
    Request,
    Route,
    Tags,
} from 'tsoa';
import { container } from '../../diod.config';
import { AbstractTaskTemplateService } from '../../domain/task/template/abstract/abstract-service';
import { AbstractUserService } from '../../domain/user/abstract/user.service';
import type { ApiRequest } from '../../shared/request.type';

// Bodies
interface TemplateCreateOneBody
    extends Partial<
        Pick<TaskTemplate, 'approvalMethod' | 'approvers' | 'label' | 'flow'>
    > {}

interface TemplateUpdateOneBody extends TemplateCreateOneBody {
    status?: TaskTemplateStatus;
}

// Responses
interface TemplateFetchAllResponse {
    requestTime: string; // ISO 8601 date-time string
    templates: TaskTemplate[];
}

interface TemplateCreateOneResponse {
    templateId: UUID;
}

interface TemplateUpdateOrDeleteOneResponse {
    success: boolean;
}

@Route('task/template')
@Tags('task-template')
export class TaskTemplateController extends Controller {
    private readonly taskTemplateService: AbstractTaskTemplateService;
    private readonly userService: AbstractUserService;

    constructor() {
        super();
        this.taskTemplateService = container.get(AbstractTaskTemplateService);
        this.userService = container.get(AbstractUserService);
    }

    @Get()
    getMany(
        @Request() request: ApiRequest,
        @Query() since?: string
    ): Promise<TemplateFetchAllResponse> {
        return this.taskTemplateService.fetchAll(request.transaction, since);
    }

    @Get('{templateId}')
    async getOne(
        @Request() request: ApiRequest,
        @Path() templateId: UUID
    ): Promise<TaskTemplate | void> {
        const templateOpt = await this.taskTemplateService.fetchById(
            request.transaction,
            templateId
        );

        if (!templateOpt.isPresent()) {
            this.setStatus(404);
            return;
        }

        return templateOpt.get();
    }

    @Post()
    async createOne(
        @Request() request: ApiRequest,
        @Body() body: TemplateCreateOneBody
    ): Promise<TemplateCreateOneResponse> {
        // TODO use `userId` from session once auth is implemented.
        const userId = await this.userService.fetchDummyUser(
            request.transaction
        );

        const templateId = await this.taskTemplateService.createOne(
            request.transaction,
            userId,
            body
        );

        return { templateId };
    }

    @Put('{templateId}')
    async updateOne(
        @Request() request: ApiRequest,
        @Path() templateId: UUID,
        @Body() body: TemplateUpdateOneBody
    ): Promise<TemplateUpdateOrDeleteOneResponse> {
        // TODO use `userId` from session once auth is implemented.
        const userId = await this.userService.fetchDummyUser(
            request.transaction
        );

        const success = await this.taskTemplateService.updateOne(
            request.transaction,
            templateId,
            userId,
            body
        );

        return { success };
    }

    @Delete('{templateId}')
    async deleteOne(
        @Request() request: ApiRequest,
        @Path() templateId: UUID
    ): Promise<TemplateUpdateOrDeleteOneResponse> {
        // TODO use `userId` from session once auth is implemented.
        const userId = await this.userService.fetchDummyUser(
            request.transaction
        );

        const success = await this.taskTemplateService.deleteOne(
            request.transaction,
            templateId,
            userId
        );

        return { success };
    }
}
