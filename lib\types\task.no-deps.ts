import type { LastModMeta, Nullable, UUID } from './common.no-deps';
import type {
    ElementCapture,
    ElementContainer,
    ElementDisplay,
    Flow,
} from './flow.no-deps';

// Statuses
export type TaskAssignmentStatus =
    | 'assignmentNotStarted' // Task has been assigned - ready for execution
    | 'assignmentInProgress' // Task execution has started
    | 'assignmentComplete'; // Task execution completed

export type TaskReportStatus =
    | 'reportInProgress' // Report has been started
    | 'reportCompleted' // Report has been completed - ready to be approved
    | 'reportApproved'; // Report has been approved

export type TaskTemplateStatus =
    | 'templateEditInProgress' // Template editing in progress
    | 'templateEditDone' // Template editing complete
    | 'templatePendingApproval' // Template is pending approval before going out
    | 'templateApproved'; // Template has been approved - ready to go out

// Task status aggregate
type TaskStatus = TaskAssignmentStatus | TaskReportStatus | TaskTemplateStatus;

// Base task definition
interface _Task extends LastModMeta {
    id: UUID;
    isDeleted: boolean;
    label: Nullable<string>;
    status: TaskStatus;
}

export interface TaskMembers {
    groups: number[];
    users: number[];
}

// Task template definitions
export type TemplateApprovalMethod = 'all' | 'atLeastOne';

export interface TemplateFlow extends Flow {}

export interface TaskTemplate extends _Task {
    approvalMethod: TemplateApprovalMethod;
    approvers: TaskMembers;
    flow: TemplateFlow;
    status: TaskTemplateStatus;
    version: number;
}

// Task assignment definitions
interface AssignmentOrigin {
    originId?: string;
    type: 'adHoc' | 'order' | 'schedule';
}

export interface AssignmentMetadata {
    origin: AssignmentOrigin;
}

export interface AssignmentElementContainer extends ElementContainer {
    assignees?: TaskMembers;
}

export type AssignmentElement =
    | AssignmentElementContainer
    | ElementCapture
    | ElementDisplay;

export interface AssignmentFlow extends Flow {
    elements: Record<UUID, AssignmentElement>;
}

export interface TaskAssignment extends _Task {
    assignees: TaskMembers;
    flow: AssignmentFlow;
    metadata: AssignmentMetadata;
    status: TaskAssignmentStatus;
    templateId: UUID;
    templateVersion: number;
}

// Answer definitions
export type AnswerId = UUID;

interface _AnswerValue<
    T extends 'number' | 'numberArray' | 'text',
    V extends number | number[] | string,
> {
    value: Nullable<V>;
    type: T;
}

export interface AnswerValueNumber extends _AnswerValue<'number', number> {}
export interface AnswerValueNumberArray
    extends _AnswerValue<'numberArray', number[]> {}
export interface AnswerValueText extends _AnswerValue<'text', string> {}

export type AnswerValue =
    | AnswerValueNumber
    | AnswerValueNumberArray
    | AnswerValueText;

interface _Answer extends LastModMeta {
    id: AnswerId;
    value: AnswerValue;
}

interface AnswerNumber extends _Answer {
    value: AnswerValueNumber;
}
interface AnswerNumberArray extends _Answer {
    value: AnswerValueNumberArray;
}
interface AnswerText extends _Answer {
    value: AnswerValueText;
}

export type Answer = AnswerNumber | AnswerNumberArray | AnswerText;

interface ReportSignature {
    createdAt?: string; // ISO 8601 date-time string
    createdBy?: number;
    value?: string;
}

export interface ReportMetadata {
    signature?: ReportSignature;
}

export interface TaskReport extends _Task {
    answers: Record<string, Answer>; // Key represents an element's path inside the assignment.
    assignmentId: UUID;
    metadata: ReportMetadata;
    status: TaskReportStatus;
}
