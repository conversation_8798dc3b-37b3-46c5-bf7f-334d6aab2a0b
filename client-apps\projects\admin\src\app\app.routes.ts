// src/app/app.routes.ts
import { Routes } from '@angular/router';

export const routes: Routes = [
    { path: '', redirectTo: 'orders', pathMatch: 'full' },
    {
        path: 'orders',
        loadChildren: () =>
            import('./routes/orders.routes').then((m) => m.ORDERS_ROUTES),
    },
    {
        path: 'login',
        loadComponent: () =>
            import('./components/login/login.component').then(
                (m) => m.LoginComponent
            ),
    },
    {
        path: 'forgot-password',
        loadComponent: () =>
            import(
                './components/forgot-password/forgot-password.component'
            ).then((m) => m.ForgotPasswordComponent),
    },
    {
        path: 'sign-up',
        loadComponent: () =>
            import('./components/sign-up/sign-up.component').then(
                (m) => m.SignUpComponent
            ),
    },
    { path: '**', redirectTo: 'orders', pathMatch: 'full' }, // Default
];
