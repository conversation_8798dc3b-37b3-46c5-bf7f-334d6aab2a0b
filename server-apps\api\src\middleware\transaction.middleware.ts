import type { Transaction } from 'kysely';
import type { NextFunction, Request, Response } from 'express';
import { Database } from '../database/database.types';
import { withTransaction } from '../database/database.util';

declare module 'express-serve-static-core' {
    interface Request {
        transaction: Transaction<Database>;
    }
}

export async function transactionMiddleware(
    req: Request,
    res: Response,
    next: NextFunction
) {
    // Provide transaction object to request handlers.
    try {
        await withTransaction(async (transaction) => {
            req.transaction = transaction;
            await next();
        });
    } catch (error) {
        console.error('Transaction error:', error);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}
