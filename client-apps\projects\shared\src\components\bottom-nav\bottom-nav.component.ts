import { CommonModule } from '@angular/common';
import { Component, HostListener, Input } from '@angular/core';

type NavItem = {
    icon: string;
    label: string;
    notificationCount?: number;
};

@Component({
    selector: 'app-bottom-nav',
    standalone: true,
    imports: [CommonModule],
    templateUrl: './bottom-nav.component.html',
    styleUrl: './bottom-nav.component.css',
})
export class BottomNavComponent {
    @Input({ required: true }) items!: NavItem[];
    @Input({ required: false }) notificationCount = 0;

    isMobile = window.innerWidth <= 768;

    @HostListener('window:resize', ['$event'])
    onResize() {
        this.isMobile = window.innerWidth <= 768;
    }
}
