import type { Nullable, UUID } from './common.no-deps';
import type { Condition, ExpressionTreeNode } from './construct.no-deps';

// Element types in alphabetical order
export type ElementTypeCapture =
    | 'captureNumber'
    | 'captureText'
    | 'captureChoiceSingle'
    | 'captureChoiceMultiple';
export type ElementTypeContainerCondition =
    | 'containerConditionPlain'
    | 'containerConditionTree';
export type ElementTypeContainer =
    | ElementTypeContainerCondition
    | 'containerSimple';
export type ElementTypeDisplay = 'displayImage' | 'displayText';

// Aggregate type for element types
export type ElementType =
    | ElementTypeCapture
    | ElementTypeContainer
    | ElementTypeDisplay;

// Base element type
interface _Element {
    id: UUID;
    label: Nullable<string>;
    type: ElementType;
}

interface WithConditionalChildren<T extends ElementContainerCondition> {
    conditionalChildren: Record<UUID, T>;
}

// Capture elements
export interface _ElementCapture
    extends _Element,
        WithConditionalChildren<ElementContainerConditionPlain> {
    type: ElementTypeCapture;
}

interface _ElementCaptureChoice extends _ElementCapture {
    options: string[];
}

export interface ElementCaptureChoiceMultiple extends _ElementCaptureChoice {
    type: 'captureChoiceMultiple';
}
export interface ElementCaptureChoiceSingle extends _ElementCaptureChoice {
    type: 'captureChoiceSingle';
}
export interface ElementCaptureNumber extends _ElementCapture {
    type: 'captureNumber';
}
export interface ElementCaptureText extends _ElementCapture {
    type: 'captureText';
}

// Aggregate type for capture elements
export type ElementCapture =
    | ElementCaptureChoiceMultiple
    | ElementCaptureChoiceSingle
    | ElementCaptureNumber
    | ElementCaptureText;

// Container elements
interface _ElementContainer extends _Element {
    children: Record<UUID, ElementEntity>;
    type: ElementTypeContainer;
}

export interface ElementContainerSimple
    extends _ElementContainer,
        WithConditionalChildren<ElementContainerConditionTree> {
    type: 'containerSimple';
}

export interface ElementContainerConditionPlain extends _ElementContainer {
    condition: Condition;
    type: 'containerConditionPlain';
}

export interface ElementContainerConditionTree extends _ElementContainer {
    root: ExpressionTreeNode<UUID>;
    type: 'containerConditionTree';
}

type ElementContainerCondition =
    | ElementContainerConditionPlain
    | ElementContainerConditionTree;

// Aggregate type for container elements
export type ElementContainer = ElementContainerSimple;

// Display elements
export interface _ElementDisplay extends _Element {
    type: ElementTypeDisplay;
    value: string; // TODO Make this into a FileId for display image elements.
}

interface ElementDisplayImage extends _ElementDisplay {
    type: 'displayImage';
}
interface ElementDisplayText extends _ElementDisplay {
    type: 'displayText';
}

// Aggregate type for display elements
export type ElementDisplay = ElementDisplayImage | ElementDisplayText;

// Aggregate type for all elements
export type ElementEntity = ElementCapture | ElementContainer | ElementDisplay;

export interface Flow {
    elements: Record<UUID, ElementEntity>;
}
