{"name": "@opex6/connected-worker-platform", "scripts": {"format": "prettier --write \"**/*.ts\"", "lint": "eslint \"**/*.ts\" --fix"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "eslint": "^9.17.0", "husky": "^9.1.7", "lint-staged": "^15.2.11", "prettier": "^3.4.2", "typescript": "^5.7.2", "typescript-eslint": "^8.18.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.ts": ["prettier --write", "eslint --fix", "git add"]}}