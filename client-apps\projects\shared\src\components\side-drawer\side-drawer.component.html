<!-- side-drawer.component.html -->
<div 
    *ngIf="isOpen"
    class="relative h-full"
>
    <!-- Drawer container -->
    <div class="h-full shadow-lg border-l border-gray-200 flex flex-col">
        <!-- Header -->
        <div class="flex items-center justify-between p-4 border-b">
            <h2 class="text-lg font-medium text-gray-900">{{ title }}</h2>
            <button
                type="button" 
                class="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none"
                (click)="close()"
            >
                <span class="sr-only">Close panel</span>
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <!-- Content -->
        <div class="flex-1 overflow-y-auto p-4">
            <ng-content></ng-content>
        </div>
    </div>
</div>