import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { IconComponent } from '../icons/icon.component';
import { FlagIconComponent } from '../icons/flag.component';
import type { Icon } from '../../types/icon.type';
import type { Tag } from '../../types/tag.type';

export interface TitleType {
    color: 'blue' | 'orange' | 'red';
    text: string;
}

@Component({
    selector: 'app-task-card',
    templateUrl: './task-card.component.html',
    imports: [CommonModule, FlagIconComponent, IconComponent],
})
export class TaskCardComponent implements OnInit {
    // Required inputs
    @Input({ required: true }) datetime!: string;
    @Input({ required: true }) description!: string;
    @Input({ required: true }) icon!: Icon;
    @Input({ required: true }) title!: TitleType;

    // Optional inputs
    @Input() hint?: string;
    @Input() tags: Tag[] = [];

    titleBoxBg!: string;
    titleDotBg!: string;

    ngOnInit(): void {
        const [titleBoxBg, titleDotBg] = this.getTitleBackgrounds();

        this.titleBoxBg = titleBoxBg;
        this.titleDotBg = titleDotBg;
    }

    private getTitleBackgrounds = (): string[] => {
        switch (this.title.color) {
            case 'blue':
                return [`bg-sky-100`, `bg-sky-500`];
            case 'orange':
                return [`bg-orange-100`, `bg-orange-500`];
            case 'red':
                return [`bg-red-100`, `bg-red-500`];
        }
    };
}
