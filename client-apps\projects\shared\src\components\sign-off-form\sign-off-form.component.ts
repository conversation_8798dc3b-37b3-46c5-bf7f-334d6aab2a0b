import {
    AngularSignaturePadModule,
    SignaturePadComponent,
} from '@almothafar/angular-signature-pad';
import { Component, ViewChild, Signal, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
    FormsModule,
    ReactiveFormsModule,
    FormBuilder,
    FormGroup,
    Validators,
} from '@angular/forms';

@Component({
    selector: 'app-sign-off-form',
    standalone: true,
    imports: [
        AngularSignaturePadModule,
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
    ],
    templateUrl: './sign-off-form.component.html',
    styleUrls: ['./sign-off-form.component.css'],
})
export class SignOffFormComponent {
    form: FormGroup;
    operatorName: Signal<string> = signal('Tablet Hovis');

    @ViewChild(SignaturePadComponent) signaturePad!: SignaturePadComponent;

    signaturePadOptions = {
        minWidth: 1,
        canvasWidth: 300,
        canvasHeight: 150,
    };

    constructor(private fb: FormBuilder) {
        this.form = this.fb.group({
            date: ['', Validators.required],
            time: ['', Validators.required],
        });
    }

    clearSignature() {
        this.signaturePad.clear();
    }

    submitForm() {
        if (this.form.valid) {
            const signatureData = this.signaturePad.isEmpty()
                ? ''
                : this.signaturePad.toDataURL();
            console.log({
                operatorName: this.operatorName(),
                ...this.form.value,
                signature: signatureData,
            });
        } else {
            console.warn('Form is invalid');
        }
    }
}
