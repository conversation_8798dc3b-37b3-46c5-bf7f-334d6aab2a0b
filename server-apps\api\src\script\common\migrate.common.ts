import * as path from 'path';
import { promises as fs } from 'fs';
import { FileMigrationProvider, MigrationResultSet, Migrator } from 'kysely';
import {
    destroyDBConnection,
    getDBConnection,
} from '../../database/database.connection';
import { ensureDatabaseExists } from '../../database/database.util';

function createMigrator(): Migrator {
    return new Migrator({
        db: getDBConnection(),
        provider: new FileMigrationProvider({
            fs,
            path,
            migrationFolder: path.join(__dirname, '../../database/migrations'),
        }),
    });
}

export async function runMigration(
    fn: (migrator: Migrator) => Promise<MigrationResultSet>
): Promise<void> {
    await ensureDatabaseExists();

    const migrator = createMigrator();

    const { error, results } = await fn(migrator);

    results?.forEach((it) => {
        if (it.status === 'Success') {
            console.log(
                `Migration "${it.migrationName}" was executed successfully`
            );
        } else if (it.status === 'Error') {
            console.error(`Failed to execute migration "${it.migrationName}"`);
        }
    });

    if (error) {
        console.error('Failed to migrate');
        console.error(error);
        process.exit(1);
    }

    await destroyDBConnection();
}
