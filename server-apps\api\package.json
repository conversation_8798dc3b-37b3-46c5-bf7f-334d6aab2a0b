{"name": "server", "version": "0.0.1", "description": "", "main": "dist/index.js", "scripts": {"build": "webpack", "dev": "ts-node-dev src/index.ts", "format": "prettier --write \"src/**/*.ts\"", "lint": "eslint \"src/**/*.ts\" --fix", "migrate-down": "ts-node src/script/migrate-down.ts", "migrate-latest": "ts-node src/script/migrate-latest.ts", "migrate-to": "ts-node src/script/migrate-to.ts", "migrate-up": "ts-node src/script/migrate-up.ts", "start": "node dist/bundle.js", "tsoa": "npx tsoa routes && npx tsoa spec"}, "dependencies": {"@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.10", "@types/ws": "^8.18.1", "bcrypt": "^6.0.0", "body-parser": "^1.20.3", "cors": "^2.8.5", "diod": "^3.0.0", "express": "^4.21.2", "google-auth-library": "^10.1.0", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "kysely": "^0.27.5", "lib": "file:../../lib", "pg": "^8.13.1", "reflect-metadata": "^0.2.2", "socket.io": "^4.8.1", "tsoa": "^6.6.0", "ws": "^8.18.2"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/node": "^22.13.1", "@types/pg": "^8.11.10", "@types/socket.io": "^3.0.1", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "ts-loader": "^9.5.1", "ts-node-dev": "^2.0.0", "typescript": "^5.7.2", "typescript-eslint": "^8.18.2", "typescript-result": "^3.1.0", "webpack": "^5.97.1", "webpack-cli": "^6.0.1", "webpack-node-externals": "^3.0.0"}}