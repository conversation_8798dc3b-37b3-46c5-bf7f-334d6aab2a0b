// accordion.component.ts
import {
    Component,
    ContentChildren,
    QueryList,
    AfterContentInit,
    Input,
} from '@angular/core';
import { AccordionItemComponent } from './accordion-item.component';

@Component({
    selector: 'app-accordion',
    standalone: true,
    template: `
        <div class="w-full max-w-2xl mx-auto">
            <ng-content></ng-content>
        </div>
    `,
})
export class AccordionComponent implements AfterContentInit {
    // Indexes of items that should be expanded by default
    @Input() expanded: number[] = [0];

    @ContentChildren(AccordionItemComponent)
    items!: QueryList<AccordionItemComponent>;

    ngAfterContentInit() {
        // Expand first item by default
        if (this.items.length > 0) {
            this.expanded.forEach((index) =>
                this.items.get(index)?.isExpanded.set(true)
            );
        }
    }
}
