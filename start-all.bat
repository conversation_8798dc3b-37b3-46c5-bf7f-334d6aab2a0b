@echo off
echo Starting all services...
echo.
echo Starting API Server in new window...
start "API Server" cmd /k "cd /d %~dp0 && start-api.bat"

timeout /t 3 /nobreak >nul

echo Starting Auth Server in new window...
start "Auth Server" cmd /k "cd /d %~dp0 && start-auth.bat"

timeout /t 3 /nobreak >nul

echo Starting Mobile App in new window...
start "Mobile App" cmd /k "cd /d %~dp0 && start-mobile.bat"

echo.
echo All services are starting...
echo API Server: http://localhost:3000
echo Auth Server: http://localhost:3001
echo Mobile App: http://localhost:4200
echo.
echo Press any key to exit...
pause >nul
