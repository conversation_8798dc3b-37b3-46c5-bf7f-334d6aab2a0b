import { interval, startWith, Subscription } from 'rxjs';
import type { Nullable } from 'lib/types/common.no-deps';
import { HeartbeatService } from './heartbeat.service';

export abstract class AbstractSyncService {
    private defaultSyncIntervalMs = 1000 * 60; // 1 minute
    private lastSyncTime: Nullable<string> = null;
    private subscription: Nullable<Subscription> = null;

    constructor(private readonly heartbeatService: HeartbeatService) {
        this.start();
    }

    abstract sync(lastSyncTime: Nullable<string>): Promise<Nullable<string>>;

    protected getLastSyncTimeParamName(): string {
        return `${this.constructor.name}_LastSyncTime`;
    }

    protected getSyncIntervalMs(): number {
        return this.defaultSyncIntervalMs;
    }

    private start = () => {
        // Initialize lastSyncTime
        const savedLastSyncTime = this.getLastSyncTimeFromLS();
        if (savedLastSyncTime) {
            this.lastSyncTime = savedLastSyncTime;
        }

        this.heartbeatService.isOnline().subscribe((isOnline) => {
            if (!isOnline && this.subscription) {
                this.subscription.unsubscribe();
                this.subscription = null;
                return;
            }

            if (isOnline && !this.subscription) {
                this.subscription = interval(this.getSyncIntervalMs())
                    .pipe(startWith(0))
                    .subscribe(this.onInterval);
            }
        });
    };

    private onInterval = async () => {
        const syncTime = await this.sync(this.lastSyncTime);

        // Update lastSyncTime when a new syncTime is given and lastSyncTime is either null or older.
        const isNewerSyncTime =
            !!syncTime &&
            (!this.lastSyncTime ||
                (this.lastSyncTime && syncTime > this.lastSyncTime));
        if (isNewerSyncTime) {
            this.lastSyncTime = syncTime;
            this.persistLastSyncToLS(syncTime);
        }
    };

    private persistLastSyncToLS = (lastSyncTime: string): void => {
        localStorage.setItem(this.getLastSyncTimeParamName(), lastSyncTime);
    };

    private getLastSyncTimeFromLS = (): Nullable<string> => {
        const saved = localStorage.getItem(this.getLastSyncTimeParamName());
        return saved ? new Date(saved).toISOString() : null;
    };
}
