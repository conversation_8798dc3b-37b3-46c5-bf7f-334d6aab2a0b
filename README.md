# OpexFlow main repository

The root npm project is mainly used for running scripts that target the entire codebase, such as `format` and `lint`, as well as being a common place for documentation.

See `package.json` -> `scripts` for a full list of commands.

## Project structure
- `/client-apps`: Contains user-facing applications. It's an Angular project with multiple sub-projects for each application.
- `/server-apps`: Contains back-end applications, such as the Auth and API servers.
- `/lib`: Common code needed by more than one application in the system (e.g. types, utils, etc);

### Use nvm to manage node (recommended)
- Install `nvm` using your favorite package manager.
- Use `nvm` to install the latest node LTS version: `$ nvm install 22.12.0`.

### System setup
1) Go to `server-apps/api` and set up the API server - follow instructions from README.
2) Go to `client-apps` and build the client-facing apps: `admin` and `mobile` - follow instructions from README.
3) Start API server.
4) Start Admin application.
5) Start Mobile application.

Note that Mobile application doesn't depend on Admin application to run (and vice-versa).