import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DropdownMenuComponent } from '@shared/components/dropdown-menu/dropdown-menu.component';
import { DropdownMenuItemComponent } from '@shared/components/dropdown-menu/dropdown-menu-item.component';
import {
    TaskCardComponent,
    TitleType,
} from '@shared/components/task-card/task-card.component';
import type { Icon } from '@shared/types/icon.type';
import type { Tag } from '@shared/types/tag.type';
import type { Nullable, UUID } from 'lib/types/common.no-deps';

export interface OrderItem {
    datetime: string;
    description: string;
    icon: Icon;
    id: UUID;
    tags: Tag[];
    title: TitleType;
    hint?: string;
}

@Component({
    selector: 'app-orders-grid',
    imports: [
        CommonModule,
        DropdownMenuComponent,
        DropdownMenuItemComponent,
        TaskCardComponent,
    ],
    templateUrl: './orders-grid.component.html',
    standalone: true,
})
export class OrdersGridComponent {
    @Input() orders: OrderItem[] = [];
    @Input() highlightedOrder: Nullable<UUID> = null;
    @Output() onAssignOrder = new EventEmitter<UUID>();

    assignOrder(orderId: UUID): void {
        this.onAssignOrder.emit(orderId);
    }
}
