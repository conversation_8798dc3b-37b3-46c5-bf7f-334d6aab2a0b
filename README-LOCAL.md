# Local Development Setup

Docker has been removed and the project is now configured to run locally.

## Prerequisites

- Node.js 22.17.1 (installed)
- npm 10.9.2 (installed)
- PostgreSQL 17 (installed)

## Database Setup

PostgreSQL is installed and configured with:
- Host: localhost
- Port: 5432
- Database: opexflow
- Username: postgres
- Password: postgres

## Quick Start

### Option 1: Start all services at once
```bash
start-all.bat
```

### Option 2: Start services individually

1. **API Server** (Port 3000):
   ```bash
   start-api.bat
   ```

2. **Auth Server** (Port 3001):
   ```bash
   start-auth.bat
   ```

3. **Mobile App** (Port 4200):
   ```bash
   start-mobile.bat
   ```

4. **Admin App** (Port 4201):
   ```bash
   start-admin.bat
   ```

## Manual Commands

If you prefer to run commands manually:

### API Server
```bash
cd server-apps/api
npm run dev
```

### Auth Server
```bash
cd server-apps/auth
npm run dev
```

### Mobile App
```bash
cd client-apps
npm run start mobile
```

### Admin App
```bash
cd client-apps
npm run start admin
```

## Environment Variables

Environment variables are configured in:
- `.env` (root)
- `server-apps/api/.env`

## Database Migrations

Database migrations have been run automatically. If you need to run them again:
```bash
cd server-apps/api
npm run migrate-latest
```

## Removed Files

The following Docker-related files have been removed:
- `compose.yaml`
- `client-apps/Dockerfile`
- `server-apps/api/Dockerfile`
- `config/nginx/` directory

## Ports

- API Server: http://localhost:3000
- Auth Server: http://localhost:3001
- Mobile App: http://localhost:4200
- Admin App: http://localhost:4201
