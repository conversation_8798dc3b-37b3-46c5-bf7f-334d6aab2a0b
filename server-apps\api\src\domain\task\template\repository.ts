import { Service } from 'diod';
import { type RawBuilder, sql } from 'kysely';
import { Optional, type UUID } from 'lib/types/common.no-deps';
import type {
    TemplateApprovalMethod,
    TemplateFlow,
    TaskMembers,
    TaskTemplate,
    TaskTemplateStatus,
} from 'lib/types/task.no-deps';
import { generateUUID } from 'lib/util/uuid';
import type { Database, Transaction } from '../../../database/database.types';
import { toJsonSqlValue } from '../../../database/database.util';
import {
    AbstractTaskTemplateRepository,
    type CreateOneDTO,
    type UpdateOneDTO,
} from './abstract/abstract-repository';

type TableRow = Omit<
    Database['task_template'],
    | 'approval_method'
    | 'created_at'
    | 'flow'
    | 'is_deleted'
    | 'last_modified_at'
    | 'status'
    | 'version'
> & {
    approval_method: TemplateApprovalMethod;
    approver_groups: number[];
    approver_users: number[];
    created_at: string;
    flow: TemplateFlow;
    is_deleted: boolean;
    last_modified_at: string;
    status: TaskTemplateStatus;
    version: number;
};

type UpdateOneValues = Partial<{
    approval_method: TemplateApprovalMethod;
    flow: RawBuilder<TemplateFlow>;
    label: string;
    status: TaskTemplateStatus;
}> & { last_modified_at: RawBuilder<string>; last_modified_by: number };

type CreateOneValues = Omit<UpdateOneValues, 'status'> & {
    created_by: number;
    id: UUID;
};

@Service()
export class TaskTemplateRepository extends AbstractTaskTemplateRepository {
    async fetchAll(
        db: Transaction,
        opts?: { since?: string }
    ): Promise<TaskTemplate[]> {
        return this.selectFromTaskTemplateTable(db)
            .where((eb) =>
                // If `since` is given, then return newer records compared to it.
                opts?.since
                    ? eb('tt.last_modified_at', '>=', opts.since)
                    : eb.val(true)
            )
            .groupBy('tt.id')
            .execute()
            .then((rows) => rows.map(this.readRow));
    }

    fetchById(
        db: Transaction,
        id: UUID,
        opts?: { excludeDeleted?: boolean }
    ): Promise<Optional<TaskTemplate>> {
        return this.selectFromTaskTemplateTable(db)
            .where('tt.id', '=', id)
            .where((eb) =>
                opts?.excludeDeleted
                    ? eb('tt.is_deleted', '=', false)
                    : eb.val(true)
            )
            .groupBy('tt.id')
            .execute()
            .then(([row]) => Optional.ofNullable(row).map(this.readRow));
    }

    async createOne(
        db: Transaction,
        userId: number,
        dto: CreateOneDTO
    ): Promise<UUID> {
        const templateId = await db
            .insertInto('task_template')
            .values(this.getCreateOneValues(userId, dto))
            .returning('id')
            .executeTakeFirstOrThrow()
            .then((result) => result.id);

        // Insert approver relations.
        if (dto.approvers) {
            await this.recreateApprovers(db, templateId, dto.approvers);
        }

        return templateId;
    }

    async updateOne(
        db: Transaction,
        id: UUID,
        userId: number,
        dto: UpdateOneDTO
    ): Promise<boolean> {
        const values = this.getUpsertOneValues(userId, dto);

        const templateUpdated = await db
            .updateTable('task_template')
            .set({ ...values, last_modified_at: sql`now()` })
            .where('id', '=', id)
            .executeTakeFirst()
            .then((result) => result.numUpdatedRows > 0);

        // Update approver relations.
        let approversUpdated = false;
        if (dto.approvers) {
            approversUpdated = await this.recreateApprovers(
                db,
                id,
                dto.approvers
            );
        }

        // TODO - check if flow was updated, and if it was,
        // then add a new flow archive entry and increment version.

        return templateUpdated || approversUpdated;
    }

    deleteOne(db: Transaction, id: UUID, userId: number): Promise<boolean> {
        return db
            .updateTable('task_template')
            .set({
                is_deleted: true,
                last_modified_at: sql`now()`,
                last_modified_by: userId,
            })
            .where('id', '=', id)
            .executeTakeFirst()
            .then(
                ({ numUpdatedRows }) => !!numUpdatedRows && numUpdatedRows > 0
            );
    }

    private selectFromTaskTemplateTable = (db: Transaction) => {
        return db
            .selectFrom('task_template as tt')
            .leftJoin(
                'task_template_approver_group as tsag',
                'tt.id',
                'tsag.task_template'
            )
            .leftJoin('user_group as ug', 'tsag.user_group', 'ug.id')
            .leftJoin(
                'task_template_approver_user as tsau',
                'tt.id',
                'tsau.task_template'
            )
            .leftJoin('user_ref as ur', 'tsau.user_ref', 'ur.id')
            .select([
                'tt.approval_method',
                'tt.created_at',
                'tt.created_by',
                'tt.flow',
                'tt.id',
                'tt.is_deleted',
                'tt.label',
                'tt.last_modified_at',
                'tt.last_modified_by',
                'tt.status',
                'tt.version',
                sql<number[]>`COALESCE(
                json_agg(DISTINCT ug.id)
                FILTER (WHERE ug.id IS NOT NULL AND NOT ug.is_deleted), '[]'
            )`.as('approver_groups'),
                sql<number[]>`COALESCE(
                json_agg(DISTINCT ur.id)
                FILTER (WHERE ur.id IS NOT NULL AND NOT ur.is_deleted), '[]'
            )`.as('approver_users'),
            ]);
    };

    private recreateApprovers = async (
        db: Transaction,
        templateId: UUID,
        approvers: TaskMembers
    ): Promise<boolean> => {
        const { groups, users } = approvers;

        // TODO Put these separate queries into a single query with multiple CTEs.
        const deletedGroups = await db
            .deleteFrom('task_template_approver_group')
            .where('task_template', '=', templateId)
            .returning('user_group')
            .execute()
            .then((rows) => rows.length > 0);

        const deletedUsers = await db
            .deleteFrom('task_template_approver_user')
            .where('task_template', '=', templateId)
            .returning('user_ref')
            .execute()
            .then((rows) => rows.length > 0);

        let insertedGroups = false;
        if (groups.length > 0) {
            await db
                .insertInto('task_template_approver_group')
                .values(
                    groups.map((group) => ({
                        task_template: templateId,
                        user_group: group,
                    }))
                )
                .returning('user_group')
                .execute();
            insertedGroups = true;
        }

        let insertedUsers = false;
        if (users.length > 0) {
            await db
                .insertInto('task_template_approver_user')
                .values(
                    users.map((user) => ({
                        task_template: templateId,
                        user_ref: user,
                    }))
                )
                .returning('user_ref')
                .execute();
            insertedUsers = true;
        }

        return deletedGroups || deletedUsers || insertedGroups || insertedUsers;
    };

    private readRow = (row: TableRow): TaskTemplate => ({
        approvalMethod: row.approval_method,
        approvers: { groups: row.approver_groups, users: row.approver_users },
        createdAt: row.created_at,
        createdBy: row.created_by,
        flow: row.flow,
        id: row.id,
        isDeleted: row.is_deleted,
        label: row.label,
        lastModifiedAt: row.last_modified_at,
        lastModifiedBy: row.last_modified_by,
        status: row.status,
        version: row.version,
    });

    private getCreateOneValues = (
        userId: number,
        dto: CreateOneDTO
    ): CreateOneValues => {
        return {
            ...this.getUpsertOneValues(userId, dto),
            created_by: userId,
            id: generateUUID(),
        };
    };

    private getUpsertOneValues = (
        userId: number,
        dto: UpdateOneDTO
    ): UpdateOneValues => {
        const values: UpdateOneValues = {
            last_modified_at: sql`now()`,
            last_modified_by: userId,
        };

        if (dto.approvalMethod) {
            values.approval_method = dto.approvalMethod;
        }

        if (dto.flow) {
            values.flow = toJsonSqlValue(dto.flow);
        }

        if (dto.label) {
            values.label = dto.label;
        }

        if (dto.status) {
            values.status = dto.status;
        }

        return values;
    };
}
