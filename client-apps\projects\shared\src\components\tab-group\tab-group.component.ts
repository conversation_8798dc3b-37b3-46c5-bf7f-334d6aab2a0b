import { animate, style, transition, trigger } from '@angular/animations';
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import type { Icon } from '../../types/icon.type';
import { IconComponent } from '../icons/icon.component';

export interface Tab {
    icon?: Icon;
    id: string;
    label: string;
    notificationCount?: number;
}

@Component({
    selector: 'app-tab-group',
    imports: [CommonModule, IconComponent],
    templateUrl: './tab-group.component.html',
    host: {
        class: 'flex flex-grow',
    },
    animations: [
        trigger('tabIndicator', [
            transition(':increment, :decrement', [
                animate('300ms ease-in-out', style({ left: '*' })),
            ]),
        ]),
    ],
})
export class TabGroupComponent {
    @Input() activeTab: string = '';
    @Input() tabs: Tab[] = [];

    @Output() actionClick = new EventEmitter<void>();
    @Output() tabChange = new EventEmitter<string>();

    selectTab(tabId: string) {
        if (this.activeTab !== tabId) {
            this.tabChange.emit(tabId);
        }
    }

    getActiveTabIndex(): number {
        return this.tabs.findIndex((tab) => tab.id === this.activeTab);
    }

    clickAction(): void {
        this.actionClick?.emit();
    }
}
