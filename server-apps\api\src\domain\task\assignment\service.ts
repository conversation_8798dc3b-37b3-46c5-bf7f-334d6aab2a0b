import { Service } from 'diod';
import {
    type ChangeSet,
    Optional,
    Result,
    type UUID,
} from 'lib/types/common.no-deps';
import type { TaskAssignment } from 'lib/types/task.no-deps';
import type { Transaction } from '../../../database/database.types';
import { AbstractTaskTemplateRepository } from '../template/abstract/abstract-repository';
import { AbstractTaskAssignmentRepository } from './abstract/abstract-repository';
import {
    AbstractTaskAssignmentService,
    type SyncDTO,
    type CreateOneDTO,
    type UpdateOneDTO,
} from './abstract/abstract-service';

@Service()
export class TaskAssignmentService extends AbstractTaskAssignmentService {
    constructor(
        private readonly assignmentRepository: AbstractTaskAssignmentRepository,
        private readonly templateRepository: AbstractTaskTemplateRepository
    ) {
        super();
    }

    fetchAll(db: Transaction, since?: string): Promise<TaskAssignment[]> {
        return this.assignmentRepository.fetchAllOrSince(db, since);
    }

    fetchById(db: Transaction, id: UUID): Promise<Optional<TaskAssignment>> {
        return this.assignmentRepository.fetchById(db, id);
    }

    async createOne(
        db: Transaction,
        userId: number,
        dto: CreateOneDTO
    ): Promise<Optional<UUID>> {
        // Fetch the target template from DB.
        const templateOpt = await this.templateRepository.fetchById(
            db,
            dto.templateId,
            { excludeDeleted: true }
        );

        // Check if template was fetched, otherwise return empty.
        if (!templateOpt.isPresent()) {
            return Optional.empty();
        }

        // Create the assignment and reutrn its ID.
        const template = templateOpt.get();
        const assignmentId = await this.assignmentRepository.createOne(
            db,
            userId,
            {
                assignees: dto.assignees,
                flow: template.flow,
                label: dto.label,
                metadata: dto.metadata,
                templateId: template.id,
                templateVersion: template.version,
            }
        );
        return Optional.of(assignmentId);
    }

    async updateOne(
        db: Transaction,
        id: UUID,
        userId: number,
        dto: UpdateOneDTO
    ): Promise<Result<boolean, string>> {
        const assignmentOpt = await this.assignmentRepository.fetchById(db, id);

        if (!assignmentOpt.isPresent()) {
            return Result.err('Assignment not found');
        }

        const success = await this.assignmentRepository.updateOne(
            db,
            id,
            userId,
            dto
        );

        return Result.ok(success);
    }

    deleteOne(db: Transaction, userId: number, id: UUID): Promise<boolean> {
        return this.assignmentRepository.deleteOne(db, userId, id);
    }

    async sync(db: Transaction, dto: SyncDTO): Promise<TaskAssignment[]> {
        // If no assignments are given then simply fetch
        // and return newer ones known by the server.
        if (dto.assignments.length === 0) {
            return this.assignmentRepository.fetchAllOrSince(
                db,
                dto.lastSyncTime
            );
        }

        // Create change set based on given assignments.
        const { toCreate, toUpdate } = await this.createSyncChangeSet(
            db,
            dto.assignments
        );

        // Create assignments that are not known to us on the server.
        const createAssignmentsP =
            toCreate.length > 0
                ? this.assignmentRepository.createBulk(db, toCreate)
                : Promise.resolve([]);

        // And update those which exist but a newer version was given.
        const updateAssignmentsP =
            toUpdate.length > 0
                ? this.assignmentRepository.updateBulk(db, toUpdate)
                : Promise.resolve([]);

        // Run create and update in parallel and await both results.
        const [createdIds, updatedIds] = await Promise.all([
            createAssignmentsP,
            updateAssignmentsP,
        ]);

        // Merge the IDs from all the assignments in the change set and
        // and fetch them from storage while also including any newer
        // assignments which might not have been part of the change set
        // (known by the server bot not by the client).
        const assignmentIds = [...createdIds, ...updatedIds];
        return assignmentIds.length > 0
            ? this.assignmentRepository.fetchByIdsOrSince(
                  db,
                  assignmentIds,
                  dto.lastSyncTime
              )
            : this.assignmentRepository.fetchAllOrSince(db, dto.lastSyncTime);
    }

    private createSyncChangeSet = async (
        db: Transaction,
        assignments: TaskAssignment[]
    ): Promise<ChangeSet<TaskAssignment, TaskAssignment>> => {
        // Lookup assignments that the server knows about and create a Map by their IDs.
        const assignmentIds = assignments.map((assignment) => assignment.id);
        const existingAssginments = await this.assignmentRepository.fetchByIds(
            db,
            assignmentIds
        );
        const existingIdMap = new Map(
            existingAssginments.map((assignment) => [assignment.id, assignment])
        );

        // Known assignments IDs exist in `existingIdSet`.
        return assignments.reduce<ChangeSet<TaskAssignment, TaskAssignment>>(
            (acc, assignment) => {
                const existingAssginment = existingIdMap.get(assignment.id);

                // If this is not a known assignment, add it to the accumulator and continue.
                if (!existingAssginment) {
                    acc.toCreate.push(assignment);
                    return acc;
                }

                // Only add the assignment to the known list of it's actually newer than the existing one.
                if (
                    new Date(assignment.lastModifiedAt) >
                    new Date(existingAssginment.lastModifiedAt)
                ) {
                    acc.toUpdate.push(assignment);
                }

                return acc;
            },
            { toCreate: [], toUpdate: [] }
        );
    };
}
