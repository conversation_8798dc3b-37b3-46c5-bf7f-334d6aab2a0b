import type { Nullable } from 'lib/types/common.no-deps';
import type {
    ConditionNumber,
    ConditionNumberArray,
    ConditionText,
    NumberExpression,
    NumberStatement,
} from 'lib/types/construct.no-deps';

function reduceNumberExpression(
    accumulator: number,
    expression: NumberExpression
): number {
    const { operator, value } = expression;

    if (value === null) {
        return accumulator;
    }

    switch (operator) {
        case '+':
            return accumulator + value;
        case '/':
            return accumulator / value;
        case '%':
            return accumulator % value;
        case '*':
            return accumulator * value;
        case '-':
            return accumulator - value;
        default:
            throw new Error(`Unknown expression operator: ${operator}`);
    }
}

function verifyNumberStatement(
    result: Nullable<number>,
    statement: NumberStatement
): boolean {
    const { operator, value } = statement;

    if (operator === '=') {
        return result === value;
    }

    if (result === null || value === null) {
        return false;
    }

    switch (operator) {
        case '<':
            return result < value;
        case '<=':
            return result <= value;
        case '>':
            return result > value;
        case '>=':
            return result >= value;
        default:
            throw new Error(`Unknown statement operator: ${operator}`);
    }
}

export function evaluateNumberCondition(
    condition: ConditionNumber,
    value: Nullable<number>
): boolean {
    const { expressions, statement } = condition;
    // If answer value is not null, apply all operations and aggregate the result.
    const result =
        value !== null
            ? expressions.reduce(reduceNumberExpression, value)
            : value;

    // Check to see if aggregated result matches terminal operation expectation.
    return verifyNumberStatement(result, statement);
}

export function evaluateNumberArrayCondition(
    condition: ConditionNumberArray,
    value: Nullable<number[]>
): boolean {
    const operator = condition.statement.operator;
    const statementValue = condition.statement.value;
    switch (operator) {
        case '=': {
            // Here, we essentially compare two number arrays
            // that can also be `null`.

            // If both statement and arg value are `null`,
            // then evaluate to `true`.
            if (statementValue === null && value === null) {
                return true;
            }

            // If at this point either statement or arg value is `null`,
            // then evalute to `false`.
            if (statementValue === null || value === null) {
                return false;
            }

            // Make sure both statement and arg value are the same length.
            if (statementValue.length !== value.length) {
                return false;
            }

            // Compare each array value from statement and arg,
            // if they don't match, then return `false`.
            for (let i = 0; i < statementValue.length; i++) {
                if (statementValue[i] !== value[i]) {
                    return false;
                }
            }

            return true;
        }
        default:
            throw new Error(
                `Unknown condition statement operator: ${operator}`
            );
    }
}

export function evaluateTextCondition(
    condition: ConditionText,
    value: Nullable<string>
): boolean {
    const operator = condition.statement.operator;
    switch (operator) {
        case '=':
            return condition.statement.value === value;
        default:
            throw new Error(
                `Unknown condition statement operator: ${operator}`
            );
    }
}
