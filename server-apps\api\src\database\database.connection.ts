import { Kysely, PostgresDialect } from 'kysely';
import type { Nullable } from 'lib/types/common.no-deps';
import { Pool } from 'pg';
import { pgPoolConfig } from './database.config';
import type { Database } from './database.types';

let db: Nullable<Kysely<Database>> = null;

export function getDBConnection(): Kysely<Database> {
    if (!db) {
        const dialect = new PostgresDialect({
            pool: new Pool(pgPoolConfig),
        });
        db = new Kysely<Database>({
            dialect,
            log: (event) => {
                if (event.level === 'error') {
                    console.error('Query failed : ', {
                        durationMs: event.queryDurationMillis,
                        error: event.error,
                        sql: event.query.sql,
                    });
                }
            },
        });
        console.log('Database connection opened.');
    }
    return db;
}

export async function destroyDBConnection(): Promise<void> {
    if (db) {
        await db.destroy();
        db = null;
        console.log('Database connection closed.');
    }
}
