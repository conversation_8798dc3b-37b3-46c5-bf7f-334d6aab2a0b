// src/app/google-sign-in/google-sign-in.component.ts
import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
    standalone: true,
    selector: 'app-google-sign-in',
    imports: [CommonModule],
    template: `
        <button (click)="signInWithGoogle()">Sign in with Google</button>
    `,
    styleUrls: ['./google-sign-in.component.css'],
})
export class GoogleSignInComponent {
    signInWithGoogle(): void {
        // Replace this with real Google auth logic
        console.log('Google sign-in clicked');
    }
}
