import type { Server as WebServer } from 'http';
import { Server } from 'socket.io';
import { WsPublisher } from '../shared/ws-publisher';

// Web Socket server
export class WsServer {
    private server: Server;

    constructor(
        readonly webServer: WebServer,
        private readonly wsPublisher: WsPublisher
    ) {
        this.server = new Server(webServer, {
            cors: {
                origin: 'http://localhost:4200', // or use "*" for development only
                methods: ['GET', 'POST'],
            },
        });

        this.start();
    }

    getSocketIoInstance(): Server {
        return this.server;
    }

    private start = (): void => {
        this.server.on('connection', (socket) => {
            socket.on('subscribe', (channel) => {
                socket.join(channel);
                console.log(`Client "${socket.id}" joined: "${channel}"`);
                this.wsPublisher.onSub(socket, channel);
            });

            socket.on('unsubscribe', (channel) => {
                socket.leave(channel);
                console.log(`Client "${socket.id}" left: "${channel}"`);
            });
        });
    };
}
