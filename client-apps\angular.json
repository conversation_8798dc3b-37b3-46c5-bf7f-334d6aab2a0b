{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"admin": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "css"}}, "root": "projects/admin", "sourceRoot": "projects/admin/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/admin", "index": "projects/admin/src/index.html", "browser": "projects/admin/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/admin/tsconfig.app.json", "inlineStyleLanguage": "css", "assets": [{"glob": "**/*", "input": "projects/admin/src/assets"}, {"glob": "**/*", "input": "projects/shared/src/assets", "output": "shared"}], "styles": ["projects/admin/src/styles.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "32kB", "maximumError": "48kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "admin:build:production"}, "development": {"buildTarget": "admin:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/admin/tsconfig.spec.json", "inlineStyleLanguage": "css", "assets": [{"glob": "**/*", "input": "projects/admin/src/assets"}, {"glob": "**/*", "input": "projects/shared/src/assets", "output": "shared"}], "styles": ["projects/admin/src/styles.css"], "scripts": []}}}}, "mobile": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "css"}}, "root": "projects/mobile", "sourceRoot": "projects/mobile/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/mobile", "index": "projects/mobile/src/index.html", "browser": "projects/mobile/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/mobile/tsconfig.app.json", "inlineStyleLanguage": "css", "assets": [{"glob": "**/*", "input": "projects/mobile/src/assets"}, {"glob": "**/*", "input": "projects/shared/src/assets", "output": "shared"}], "styles": ["projects/mobile/src/styles.css", "projects/shared/src/styles/base.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all", "serviceWorker": "projects/mobile/ngsw-config.json"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "serviceWorker": "projects/mobile/ngsw-config.json"}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "mobile:build:production"}, "development": {"buildTarget": "mobile:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/mobile/tsconfig.spec.json", "inlineStyleLanguage": "css", "assets": [{"glob": "**/*", "input": "projects/mobile/src/assets"}, {"glob": "**/*", "input": "projects/shared/src/assets", "output": "shared"}], "styles": ["projects/mobile/src/styles.css"], "scripts": []}}}}, "shared": {"projectType": "library", "root": "projects/shared", "sourceRoot": "projects/shared/src", "prefix": "shared", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/shared/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/shared/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/shared/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"tsConfig": "projects/shared/tsconfig.spec.json", "polyfills": ["zone.js", "zone.js/testing"]}}, "storybook": {"builder": "@storybook/angular:start-storybook", "options": {"configDir": "projects/shared/.storybook", "browserTarget": "shared:build", "compodoc": true, "compodocArgs": ["-e", "json", "-d", "projects/shared"], "port": 6006}}, "build-storybook": {"builder": "@storybook/angular:build-storybook", "options": {"configDir": "projects/shared/.storybook", "browserTarget": "shared:build", "compodoc": true, "compodocArgs": ["-e", "json", "-d", "projects/shared"], "outputDir": "dist/storybook/shared"}}}}}, "cli": {"analytics": "454507b4-a938-4e19-8c9b-8de46d1bc1e7"}}