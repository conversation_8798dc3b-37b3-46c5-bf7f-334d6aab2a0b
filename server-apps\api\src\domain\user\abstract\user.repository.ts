import type { Optional, UUID } from 'lib/types/common.no-deps';
import type { Transaction } from '../../../database/database.types';

export interface FetchUserDto {
    id: number;
    shareId: UUID;
    email: string;
    passwordHash: string;
}

export interface CreateUserDto {
    shareId: UUID;
    userName: string;
    email: string;
    passwordHash: string;
}

export abstract class AbstractUserRepository {
    abstract fetchAllUsers(db: Transaction): Promise<FetchUserDto[]>;

    abstract fetchUserById(
        db: Transaction,
        userId: number
    ): Promise<Optional<FetchUserDto>>;

    abstract createUser(db: Transaction, dto: CreateUserDto): Promise<number>;

    abstract updateUser(
        db: Transaction,
        userId: number,
        dto: Partial<CreateUserDto>
    ): Promise<boolean>;

    abstract deleteUserById(db: Transaction, userId: number): Promise<boolean>;

    abstract fetchUserByEmail(
        db: Transaction,
        email: string
    ): Promise<Optional<FetchUserDto>>;
}
