import { provideHttpClient, withInterceptors } from '@angular/common/http';
import {
    type ApplicationConfig,
    provideZoneChangeDetection,
    isDevMode,
    provideAppInitializer,
    inject,
} from '@angular/core';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideRouter } from '@angular/router';
import { provideServiceWorker } from '@angular/service-worker';
import { routes } from './app.routes';
import { baseUrlInterceptor } from './core/interceptors/base-url.interceptor';
import { TaskReportSyncService } from './domain/tasks/reports/report-sync.service';
import { TaskTemplateSyncService } from './domain/tasks/templates/template-sync.service';

export const appConfig: ApplicationConfig = {
    providers: [
        provideZoneChangeDetection({ eventCoalescing: true }),
        provideRouter(routes),
        provideServiceWorker('ngsw-worker.js', {
            enabled: !isDevMode(),
            registrationStrategy: 'registerWhenStable:30000',
        }),
        provideAnimationsAsync(),
        provideHttpClient(withInterceptors([baseUrlInterceptor])),
        provideAppInitializer(() => {
            inject(TaskTemplateSyncService);
            inject(TaskReportSyncService);
        }),
    ],
};
