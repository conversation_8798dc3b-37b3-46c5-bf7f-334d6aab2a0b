import type { Nullable } from './common.no-deps';

// Operators
export type LogicalOperator = '&' | '|' | '~';
type ArithmeticOperator = '+' | '/' | '%' | '*' | '-';
type ComparisonOperator = '=' | '<' | '<=' | '>' | '>=';

// Construct - the basis for both expressions and statements
type ConstructOperator = ArithmeticOperator | ComparisonOperator;
type Construct<O extends ConstructOperator, V> = {
    operator: O;
    value: Nullable<V>;
};

// Expressions
type ExpressionType = 'number' | 'string';
type _Expression<T extends ExpressionType> = { type: T };
export type NumberExpression = Construct<ArithmeticOperator, number> &
    _Expression<'number'>;
type StringExpression = Construct<'+', string> & _Expression<'string'>;
export type Expression = NumberExpression | StringExpression;

// Statements
export type StatementType = 'number' | 'numberArray' | 'text';
type _Statement<T extends StatementType> = { type: T };
export type NumberStatement = Construct<ComparisonOperator, number> &
    _Statement<'number'>;
export type NumberArrayStatement = Construct<'=', number[]> &
    _Statement<'numberArray'>;
export type StringStatement = Construct<'=', string> & _Statement<'text'>;
export type Statement =
    | NumberStatement
    | NumberArrayStatement
    | StringStatement;

// Conditions
type _Condition<S extends Statement, T extends StatementType> = {
    statement: S;
    type: T;
};
export type ConditionNumber = _Condition<NumberStatement, 'number'> & {
    expressions: NumberExpression[];
};
export type ConditionNumberArray = _Condition<
    NumberArrayStatement,
    'numberArray'
>;
export type ConditionText = _Condition<StringStatement, 'text'>;
export type Condition = ConditionNumber | ConditionNumberArray | ConditionText;

// Expression tree
type _ExpressionTreeNode<T extends 'inner' | 'outer'> = {
    type: T;
};

type ExpressionTreeNodeInner<I> = _ExpressionTreeNode<'inner'> & {
    left: ExpressionTreeNode<I>;
    right: ExpressionTreeNode<I>;
    operator: LogicalOperator;
};

type ExpressionTreeNodeOuter<I> = _ExpressionTreeNode<'outer'> & {
    condition: Condition;
    id: I;
};

export type ExpressionTreeNode<I> =
    | ExpressionTreeNodeInner<I>
    | ExpressionTreeNodeOuter<I>;
