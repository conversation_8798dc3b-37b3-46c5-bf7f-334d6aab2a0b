import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';

@Component({
    selector: 'app-loading',
    imports: [CommonModule],
    templateUrl: './loading.component.html',
    styleUrl: './loading.component.css',
})
export class LoadingComponent implements OnInit {
    @Input() message: string = 'Loading...'; // Default loading message
    @Input() size: string = '50px'; // Spinner size holmie c'mon now watchu think this is
    @Input() color: string = '#3498db'; // Default color foo BAM!

    isVisible: boolean = true;
    actualSize: string = '';

    ngOnInit() {
        this.calculateResponsiveSize();
        window.addEventListener('resize', () => this.calculateResponsiveSize());
    }

    private calculateResponsiveSize(): void {
        const screenWidth = window.innerWidth;
        if (screenWidth < 768) {
            // mobile
            this.actualSize = `${parseInt(this.size) * 0.6}px`;
        } else if (screenWidth < 1024) {
            // tablet
            this.actualSize = `${parseInt(this.size) * 0.8}px`;
        } else {
            // desktop
            this.actualSize = this.size;
        }
    }

    hideSpinner(): void {
        this.isVisible = false;
    }
}
