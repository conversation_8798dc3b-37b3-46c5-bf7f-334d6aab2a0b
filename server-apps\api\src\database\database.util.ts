import { type Raw<PERSON><PERSON><PERSON>, sql } from 'kysely';
import { Pool } from 'pg';
import { pgPoolConfig as config } from './database.config';
import { getDBConnection } from './database.connection';
import { Transaction } from './database.types';

/**
 * Ensure the database exists before running migrations
 */
export async function ensureDatabaseExists(): Promise<void> {
    // Connect to the default "postgres" database to check if our database exists
    const pool = new Pool({ ...config, database: 'postgres' });

    try {
        // Check if the database exists
        console.log(`Checking if database "${config.database}" exists...`);
        const result = await pool.query(
            `SELECT 1 FROM pg_database WHERE datname = $1`,
            [config.database]
        );

        if (result.rowCount === 0) {
            console.log(
                `Database "${config.database}" does not exist. Creating...`
            );
            await pool.query(`CREATE DATABASE "${config.database}"`);
            console.log(`Database "${config.database}" created.`);
        } else {
            console.log(`Database "${config.database}" already exists.`);
        }
    } catch (error) {
        console.error('Error ensuring database exists:', error);
    } finally {
        await pool.end();
    }
}

/**
 * Converts given object into database-friendly JSON.
 * Use this whenever you need to insert ore update a record containing a JSON column.
 */
export function toJsonSqlValue<T>(obj: T): RawBuilder<T> {
    return sql`${JSON.stringify(obj)}`;
}

/**
 * Uses the database connection to create a transaction, then
 * passes it over to the `fn` argument.
 *
 * @param fn - transaction handler
 */
export function withTransaction(
    fn: (tx: Transaction) => Promise<void>
): Promise<unknown> {
    return getDBConnection().transaction().execute(fn);
}
