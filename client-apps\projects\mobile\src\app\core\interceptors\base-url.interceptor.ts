import type { HttpInterceptorFn } from '@angular/common/http';
import { isDevMode } from '@angular/core';

const baseUrl = isDevMode() ? 'http://localhost:3000' : '/api';

export const baseUrlInterceptor: HttpInterceptorFn = (req, next) => {
    // Skip absolute URLs
    if (req.url.startsWith('http')) {
        return next(req);
    }

    // Prepend the base URL to the request's URL
    const modifiedReq = req.clone({ url: `${baseUrl}${req.url}` });
    return next(modifiedReq);
};
