import { Service } from 'diod';
import type { Optional, UUID } from 'lib/types/common.no-deps';
import type { TaskTemplate } from 'lib/types/task.no-deps';
import type { Transaction } from '../../../database/database.types';
import { AbstractTaskTemplateRepository } from './abstract/abstract-repository';
import {
    AbstractTaskTemplateService,
    type CreateOneDTO,
    type TemplateFetchAllResponse,
    type UpdateOneDTO,
} from './abstract/abstract-service';

@Service()
export class TaskTemplateService extends AbstractTaskTemplateService {
    constructor(
        private readonly taskTemplateRepository: AbstractTaskTemplateRepository
    ) {
        super();
    }

    async fetchAll(
        db: Transaction,
        since?: string
    ): Promise<TemplateFetchAllResponse> {
        const requestTime = new Date().toISOString();
        const templates = await this.taskTemplateRepository.fetchAll(db, {
            since,
        });

        return { requestTime, templates };
    }

    fetchById(db: Transaction, id: UUID): Promise<Optional<TaskTemplate>> {
        return this.taskTemplateRepository.fetchById(db, id);
    }

    createOne(
        db: Transaction,
        userId: number,
        dto: CreateOneDTO
    ): Promise<UUID> {
        return this.taskTemplateRepository.createOne(db, userId, dto);
    }

    updateOne(
        db: Transaction,
        id: UUID,
        userId: number,
        dto: UpdateOneDTO
    ): Promise<boolean> {
        // TODO Increment version number.
        return this.taskTemplateRepository.updateOne(db, id, userId, dto);
    }

    deleteOne(db: Transaction, id: UUID, userId: number): Promise<boolean> {
        return this.taskTemplateRepository.deleteOne(db, id, userId);
    }
}
