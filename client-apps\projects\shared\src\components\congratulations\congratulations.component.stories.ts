import {
    Meta,
    moduleMetadata as storybookModuleMetadata,
    StoryObj,
} from '@storybook/angular';
import { CongratulationsComponent } from './congratulations.component';

const meta: Meta<CongratulationsComponent> = {
    title: 'Components/Congratulations',
    component: CongratulationsComponent,
    decorators: [
        // Provide necessary imports for the component
        storybookModuleMetadata({
            imports: [],
        }),
    ],
    // Add any parameters needed for the story
    parameters: {
        layout: 'centered',
    },
};

export default meta;
type Story = StoryObj<CongratulationsComponent>;

// Default story
export const Default: Story = {
    args: {},
};

// You can add more story variants here if needed
