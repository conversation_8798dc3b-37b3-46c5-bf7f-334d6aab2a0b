import { db } from '../utils/database.util';

/**
 * Decorator factory that wraps a function/method in a Dexie transaction
 *
 * @param mode - Transaction mode: 'r' for readonly or 'rw' for read-write (default: 'rw')
 */
export function Transactional(mode: 'r' | 'rw' = 'rw') {
    return function (
        target: any,
        propertyKey: string,
        descriptor: PropertyDescriptor
    ) {
        // Store the original method
        const originalMethod = descriptor.value;

        // Replace the original method with the wrapped one
        descriptor.value = async function (...args: any[]) {
            // Execute the original method inside a transaction and return its result directly
            return await db.transaction(mode, db.allTables(), () => {
                return originalMethod.apply(this, args);
            });
        };

        return descriptor;
    };
}
