<div class="flex flex-col flex-grow overflow-hidden">
    <!-- Header Section -->
    <header class="shadow-md py-2 px-4 z-10">
        <div class="flex items-center">
            <!-- Tab Group Selector -->
            <app-tab-group
                [activeTab]="activeTab"
                [tabs]="tabs"
                (tabChange)="setActiveTab($event)"
            ></app-tab-group>

            <!-- Search Bar - Conditionally shown -->
            <div *ngIf="showSearch" class="flex flex-grow">
                <div class="flex flex-grow"></div>
                <div class="relative flex rounded-md h-8 bg-gray-50">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <input
                        type="text" 
                        [(ngModel)]="searchQuery"
                        (keyup.enter)="onSearch()"
                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 placeholder-gray-500 focus:outline-none focus:ring-sky-600 focus:border-sky-600 sm:text-sm" 
                        [placeholder]="searchPlaceholder"
                    >
                </div>
            </div>
        </div>
    </header>

    <!-- Body Section - Content projected from parent -->
    <main class="flex flex-grow overflow-auto">
        <ng-content></ng-content>
    </main>
</div>