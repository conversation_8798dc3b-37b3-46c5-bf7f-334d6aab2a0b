import { Component, EventEmitter, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';

@Component({
    selector: 'app-filter-bar',
    templateUrl: './filter-bar.component.html',
    styleUrls: ['./filter-bar.component.css'],
    standalone: true,
    imports: [FormsModule],
})
export class FilterBarComponent {
    searchQuery: string = '';

    @Output() searchChange = new EventEmitter<string>();
    @Output() filterClick = new EventEmitter<void>();

    onSearchInput() {
        this.searchChange.emit(this.searchQuery);
    }

    onFilterClick() {
        this.filterClick.emit();
    }
}
