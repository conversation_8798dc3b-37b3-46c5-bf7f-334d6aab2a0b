import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import type { Icon } from '@shared/types/icon.type';
import { SimpleListItemComponent } from './simple-list-item.component';

export interface ListItem {
    icon: Icon;
    id: string;
    subtitle: string;
    title: string;
}

@Component({
    selector: 'app-simple-list',
    imports: [CommonModule, SimpleListItemComponent],
    template: `
        <div class="flex max-w overflow-auto">
            <div class="flex flex-col flex-grow space-y-4 p-4">
                <app-simple-list-item
                    *ngFor="let item of items"
                    [icon]="item.icon"
                    [title]="item.title"
                    [subtitle]="item.subtitle"
                    (itemClick)="onItemClick(item.id)"
                ></app-simple-list-item>
            </div>
        </div>
    `,
})
export class SimpleListComponent {
    @Input() items: ListItem[] = [];

    @Output() itemClick = new EventEmitter<string>();

    onItemClick(itemId: string) {
        this.itemClick.emit(itemId);
    }
}
