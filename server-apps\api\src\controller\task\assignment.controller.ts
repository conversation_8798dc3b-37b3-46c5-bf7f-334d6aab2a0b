import type {
    AssignmentFlow,
    AssignmentMetadata,
    TaskAssignment,
    TaskAssignmentStatus,
    TaskMembers,
} from 'lib/types/task.no-deps';
import type { UUID } from 'lib/types/common.no-deps';
import {
    Body,
    Controller,
    Delete,
    Get,
    Path,
    Post,
    Put,
    Query,
    Request,
    Route,
    Tags,
} from 'tsoa';
import { container } from '../../diod.config';
import { AbstractTaskAssignmentService } from '../../domain/task/assignment/abstract/abstract-service';
import { AbstractUserService } from '../../domain/user/abstract/user.service';
import type { ApiRequest } from '../../shared/request.type';

// Bodies
interface AssignmentCreateOneBody {
    assignees?: TaskMembers;
    flow?: AssignmentFlow;
    label?: string;
    metadata: AssignmentMetadata;
    templateId: UUID;
}

interface AssignmentUpdateOneBody {
    assignees?: TaskMembers;
    flow?: AssignmentFlow;
    status?: TaskAssignmentStatus;
}

// Response
interface AssignmentFetchAllResponse {
    assignments: TaskAssignment[];
}

interface AssignmentCreateOneResponse {
    assignmentId: UUID;
}

interface AssignmentUpdateOrDeleteOneResponse {
    error?: string;
    success: boolean;
}

@Route('task/assignment')
@Tags('task-assignment')
export class TaskAssignmentController extends Controller {
    private readonly taskAssignmentService: AbstractTaskAssignmentService;
    private readonly userService: AbstractUserService;

    constructor() {
        super();
        this.taskAssignmentService = container.get(
            AbstractTaskAssignmentService
        );
        this.userService = container.get(AbstractUserService);
    }

    @Get()
    async getMany(
        @Request() request: ApiRequest,
        @Query() since?: string
    ): Promise<AssignmentFetchAllResponse> {
        const assignments = await this.taskAssignmentService.fetchAll(
            request.transaction,
            since
        );

        return { assignments };
    }

    @Get('{assignmentId}')
    async getOne(
        @Request() request: ApiRequest,
        @Path() assignmentId: UUID
    ): Promise<TaskAssignment | void> {
        const assignmentOpt = await this.taskAssignmentService.fetchById(
            request.transaction,
            assignmentId
        );

        if (!assignmentOpt.isPresent()) {
            this.setStatus(404);
            return;
        }

        return assignmentOpt.get();
    }

    @Post()
    async createOne(
        @Request() request: ApiRequest,
        @Body() body: AssignmentCreateOneBody
    ): Promise<AssignmentCreateOneResponse | void> {
        // TODO Use userId from session once auth is implemented.
        const userId = await this.userService.fetchDummyUser(
            request.transaction
        );

        const assignmentIdOpt = await this.taskAssignmentService.createOne(
            request.transaction,
            userId,
            body
        );

        if (!assignmentIdOpt.isPresent()) {
            this.setStatus(404);
            return;
        }

        return { assignmentId: assignmentIdOpt.get() };
    }

    @Put('{assignmentId}')
    async updateOne(
        @Request() request: ApiRequest,
        @Path() assignmentId: UUID,
        @Body() body: AssignmentUpdateOneBody
    ): Promise<AssignmentUpdateOrDeleteOneResponse> {
        // TODO Use userId from session once auth is implemented.
        const userId = await this.userService.fetchDummyUser(
            request.transaction
        );

        const result = await this.taskAssignmentService.updateOne(
            request.transaction,
            assignmentId,
            userId,
            body
        );

        if (result.isErr()) {
            this.setStatus(400);
            return { error: result.unwrapErr(), success: false };
        }

        return { success: result.unwrap() };
    }

    @Delete('{assignmentId}')
    async deleteOne(
        @Request() request: ApiRequest,
        @Path() assignmentId: UUID
    ): Promise<AssignmentUpdateOrDeleteOneResponse> {
        // TODO Use userId from session once auth is implemented.
        const userId = await this.userService.fetchDummyUser(
            request.transaction
        );

        const success = await this.taskAssignmentService.deleteOne(
            request.transaction,
            userId,
            assignmentId
        );

        return { success };
    }
}
