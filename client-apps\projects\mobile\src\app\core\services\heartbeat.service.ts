import { Injectable } from '@angular/core';
import { BehaviorSubject, interval, type Observable, startWith } from 'rxjs';
import { HttpClientService } from './http-client.service';

@Injectable({
    providedIn: 'root',
})
export class HeartbeatService {
    private readonly checkInterval = 1000 * 30; // 30 seconds
    private readonly isOnlineSubject = new BehaviorSubject<boolean>(false);

    constructor(private readonly httpClient: HttpClientService) {
        this.startMonitoring();
    }

    isOnline(): Observable<boolean> {
        return this.isOnlineSubject.asObservable();
    }

    private startMonitoring = (): void => {
        interval(this.checkInterval)
            .pipe(startWith(0))
            .subscribe(() => {
                this.checkHearbeat();
            });
    };

    private checkHearbeat = (): void => {
        this.httpClient.getHeartbeat().subscribe({
            next: () => {
                this.isOnlineSubject.next(true);
            },
            error: () => {
                this.isOnlineSubject.next(false);
            },
        });
    };
}
