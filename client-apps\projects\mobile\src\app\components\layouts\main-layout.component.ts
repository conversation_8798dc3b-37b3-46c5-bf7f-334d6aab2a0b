import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { EventType, Router, RouterOutlet } from '@angular/router';
import { FloatingActionButtonComponent } from '@shared/components/buttons/floating-action-button.component';
import { TabGroupComponent } from '@shared/public-api';
import type { Icon } from '@shared/types/icon.type';
import type { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { ConstSet } from 'lib/util/set';
import { NavigationService } from '../../core/services/navigation.service';

const tabIds = ['actions', 'assignments', 'notifications', 'search'] as const;
const tabIdSet = new ConstSet(tabIds);

interface Tab {
    id: (typeof tabIds)[number];
    label: string;
    icon?: Icon;
    notificationCount?: number;
}

@Component({
    selector: 'app-main-layout',
    imports: [FloatingActionButtonComponent, RouterOutlet, TabGroupComponent],
    templateUrl: './main-layout.component.html',
    styles: [
        `
            .tab-group-container {
                box-shadow: 0px -0.8rem 0.8rem rgba(0, 0, 0, 0.08);
            }
        `,
    ],
})
export class MainLayoutComponent implements OnDestroy, OnInit {
    tabs: Tab[] = [
        { icon: 'calendar', id: 'assignments', label: 'Tasks' },
        { icon: 'search', id: 'search', label: 'Search' },
        { icon: 'bolt', id: 'actions', label: 'Actions' },
        {
            icon: 'bell',
            id: 'notifications',
            label: 'Notifications',
            notificationCount: 7,
        },
    ];

    activeTab = 'assignments';

    private routerUrlSubscription!: Subscription;

    constructor(
        private readonly navigationService: NavigationService,
        private readonly router: Router
    ) {}

    ngOnInit(): void {
        this.subscribeToUrlChanges();
    }

    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions.
        this.routerUrlSubscription.unsubscribe();
    }

    onTabChange(tabId: string): void {
        this.routeToNewSelectedTab(tabIdSet.assertAndReturn(tabId));
    }

    onClickNewAssignment(): void {
        this.navigationService.openBottomSheet();
    }

    private subscribeToUrlChanges(): void {
        this.setActiveTabBasedOnUrl();

        this.routerUrlSubscription = this.router.events
            .pipe(filter((event) => event.type === EventType.NavigationEnd))
            .subscribe(this.setActiveTabBasedOnUrl);
    }

    private setActiveTabBasedOnUrl = (): void => {
        // We are only intere*sted in a specific part of the full URL.
        const path = this.router.url.split(/^\?|(?=\()|\//g)[1];

        this.activeTab = tabIdSet.assertAndReturn(path);
    };

    private routeToNewSelectedTab = (tabId: Tab['id']): void => {
        if (this.activeTab === tabId) {
            return;
        }

        switch (tabId) {
            case 'assignments':
                this.navigationService.routeToAssignmentsTab();
                break;
            case 'search':
                this.navigationService.routeToSearchMainTab();
                break;
            case 'actions':
                this.navigationService.routeToActionsMainTab();
                break;
            case 'notifications':
                this.navigationService.routeToNotificationsTab();
                break;
            default:
                throw new Error(`Not implemented: ${tabId}`);
        }
    };
}
