import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { EventType, Router, RouterOutlet } from '@angular/router';
import { TabContainerComponent } from '@shared/components/tab-container/tab-container.component';
import type { Tab } from '@shared/components/tab-group/tab-group.component';
import { ConstSet } from 'lib/util/set';
import { filter, type Subscription } from 'rxjs';
import { DashboardLayoutComponent } from '../../dashboard-layout/dashboard-layout.component';

const tabIds = ['assigned', 'pending'] as const;
const tabIdSet = new ConstSet(tabIds);

@Component({
    selector: 'app-orders',
    imports: [
        CommonModule,
        DashboardLayoutComponent,
        RouterOutlet,
        TabContainerComponent,
    ],
    templateUrl: './orders.component.html',
})
export class OrdersComponent implements OnInit, OnDestroy {
    tabs: Tab[] = [
        { id: 'pending', label: 'Pending' },
        { id: 'assigned', label: 'Assigned' },
    ];

    activeTab = 'pending';
    searchQuery = '';

    private routerUrlSubscription!: Subscription;

    constructor(private readonly router: Router) {}

    ngOnInit(): void {
        this.subscribeToUrlChanges();
    }

    ngOnDestroy(): void {
        this.routerUrlSubscription.unsubscribe();
    }

    onTabChange(tabId: string): void {
        this.activeTab = tabId;
        this.router.navigate(['/orders', tabId]);
    }

    onSearch(event: { query: string; activeTab: string }): void {
        this.searchQuery = event.query;
        // Implement search logic based on active tab
        console.log(`Searching for "${event.query}" in ${event.activeTab}`);
    }

    private subscribeToUrlChanges(): void {
        this.setActiveTabBasedOnUrl();

        this.routerUrlSubscription = this.router.events
            .pipe(filter((event) => event.type === EventType.NavigationEnd))
            .subscribe(this.setActiveTabBasedOnUrl);
    }

    private setActiveTabBasedOnUrl = (): void => {
        // We are only interested in a specific part of the full URL.
        const path = this.router.url.split(/^\?|(?=[(?])|\/|\?.*$/g)[2];

        this.activeTab = tabIdSet.assertAndReturn(path);
    };
}
