import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import type { AssignmentElement, AssignmentFlow } from 'lib/types/task.no-deps';
import { ElementComponent } from './element.component';

@Component({
    selector: 'app-flow',
    imports: [CommonModule, ElementComponent],
    templateUrl: './flow.component.html',
})
export class FlowComponent implements OnInit {
    @Input({ required: true }) flow!: AssignmentFlow;

    elements!: AssignmentElement[]; // Instantiated in `ngOnInit`.

    ngOnInit(): void {
        this.elements = Object.values(this.flow.elements);
    }
}
