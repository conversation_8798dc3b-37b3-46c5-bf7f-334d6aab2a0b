import type { UUID } from 'lib/types/common.no-deps';
import type { Order } from 'lib/types/order.no-deps';
import { Body, Controller, Get, Post, Request, Route, Tags } from 'tsoa';
import { container } from '../diod.config';
import { AbstractOrderService } from '../domain/order/abstract/abstract-service';
import type { ApiRequest } from '../shared/request.type';

// Bodies
interface OrderCreateOneBody
    extends Omit<Order, 'createdAt' | 'id' | 'status'> {
    status?: Order['status'];
}

// Responses
interface OrderFetchAllResponse {
    orders: Order[];
}

interface OrderCreateOneResponse {
    orderId: UUID;
}

@Route('order')
@Tags('order')
export class OrderController extends Controller {
    private readonly orderService: AbstractOrderService;

    constructor() {
        super();
        this.orderService = container.get(AbstractOrderService);
    }

    @Get()
    getMany(@Request() request: ApiRequest): Promise<OrderFetchAllResponse> {
        return this.orderService
            .fetchAll(request.transaction)
            .then((orders) => ({ orders }));
    }

    @Post()
    async createOne(
        @Request() request: ApiRequest,
        @Body() body: OrderCreateOneBody
    ): Promise<OrderCreateOneResponse | void> {
        const orderIdOpt = await this.orderService.createOne(
            request.transaction,
            body
        );

        if (!orderIdOpt.isPresent()) {
            this.setStatus(400);
            return;
        }

        const orderId = orderIdOpt.get();

        return { orderId };
    }
}
