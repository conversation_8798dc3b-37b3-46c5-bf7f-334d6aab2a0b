import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CheckmarkIconComponent } from '@shared/components/icons/checkmark.component';
import { LeftChevronIconComponent } from '@shared/components/icons/left-chevron.component';
import type { Nullable } from 'lib/types/common.no-deps';

@Component({
    selector: 'app-page-header',
    imports: [CheckmarkIconComponent, LeftChevronIconComponent],
    templateUrl: './page-header.component.html',
    styleUrl: './page-header.component.css',
})
export class PageHeaderComponent {
    @Input() label: Nullable<string> = null;

    @Output() onLeftAction = new EventEmitter();
    @Output() onRightAction = new EventEmitter();

    onLeftActionClick(): void {
        this.onLeftAction.emit();
    }

    onRightActionClick(): void {
        this.onRightAction.emit();
    }
}
