import { Service } from 'diod';
import type { TaskReport, Answer } from 'lib/types/task.no-deps';
import type { ChangeSet, UUID } from 'lib/types/common.no-deps';
import type { Transaction } from '../../../database/database.types';
import { AbstractTaskAssignmentService } from '../assignment/abstract/abstract-service';
import { AbstractTaskReportRepository } from './abstract/abstract-repository';
import {
    AbstractTaskReportService,
    type SyncRequestDTO,
    type SyncResponseDTO,
} from './abstract/abstract-service';

@Service()
export class TaskReportService extends AbstractTaskReportService {
    constructor(
        private readonly assignmentService: AbstractTaskAssignmentService,
        private readonly reportRepository: AbstractTaskReportRepository
    ) {
        super();
    }

    async sync(db: Transaction, dto: SyncRequestDTO): Promise<SyncResponseDTO> {
        const requestTime = new Date().toISOString();
        const assignments = await this.assignmentService.sync(db, dto);
        const reports = await this.syncReports(db, dto);

        return { assignments, reports, requestTime };
    }

    private syncReports = async (
        db: Transaction,
        dto: Omit<SyncRequestDTO, 'assignments'>
    ): Promise<TaskReport[]> => {
        // Nothing to do if no reports are given.
        if (dto.reports.length === 0) {
            // Just fetch newer reports that the server might know about.
            return this.reportRepository.fetchAll(db, {
                since: dto.lastSyncTime,
            });
        }

        // Create change set based on given reports.
        const { toCreate, toUpdate } = await this.createSyncChangeSet(
            db,
            dto.reports
        );

        // Create reports that are not known to us on the server.
        const createReportsP =
            toCreate.length > 0
                ? this.reportRepository.createBulk(db, toCreate)
                : Promise.resolve([]);

        // And update those which exist but a newer version was given.
        const updateReportsP =
            toUpdate.length > 0
                ? this.updateKnownReports(db, toUpdate)
                : Promise.resolve([]);

        // Run create and update in parallel and await both results.
        const [createdIds, updatedIds] = await Promise.all([
            createReportsP,
            updateReportsP,
        ]);

        // Merge the IDs from all the reports in the change set and
        // and fetch them from storage while also including any newer
        // reports which might not have been part of the change set
        // (known by the server bot not by the client).
        const reportIds = [...createdIds, ...updatedIds];
        return reportIds.length > 0
            ? this.reportRepository.fetchByIdsOrSince(
                  db,
                  reportIds,
                  dto.lastSyncTime
              )
            : this.reportRepository.fetchAll(db, {
                  since: dto.lastSyncTime,
              });
    };

    private createSyncChangeSet = async (
        db: Transaction,
        reports: TaskReport[]
    ): Promise<ChangeSet<TaskReport, TaskReport>> => {
        // Lookup reports that the server knows about and put their IDs in a Set.
        const reportIds = reports.map((report) => report.id);
        const existingReports = await this.reportRepository.fetchByIds(
            db,
            reportIds
        );
        const existingIdSet = new Set(
            existingReports.map((assignment) => assignment.id)
        );

        // Known reports IDs exist in `existingIdSet`.
        return reports.reduce<ChangeSet<TaskReport, TaskReport>>(
            (acc, report) => {
                if (existingIdSet.has(report.id)) {
                    acc.toUpdate.push(report);
                } else {
                    acc.toCreate.push(report);
                }
                return acc;
            },
            { toCreate: [], toUpdate: [] }
        );
    };

    private updateKnownReports = async (
        db: Transaction,
        reports: TaskReport[]
    ): Promise<UUID[]> => {
        // Map reports to their IDs. Use `.reduce` instead of `.map` because
        // ID is nullable in the data-type. We don't actually expect null IDs here.
        const ids = reports.reduce<UUID[]>((acc, report) => {
            if (report.id !== null) {
                acc.push(report.id);
            }
            return acc;
        }, []);

        // Fetch current reports from storage by IDs.
        const currentReports = await this.reportRepository.fetchByIds(db, ids);

        // Turn reports from Array to Map (ID is key) for faster lookup later.
        const currentReportsMap = new Map(
            currentReports.map((report) => [report.id, report])
        );

        // Compare current (essentially existing) reports with
        // the ones received as argument in order to determine which
        // reports need to be updated inside our storage as well.
        const reportsToUpdate = reports.map((updated) => {
            const current = currentReportsMap.get(updated.id);

            if (!current) {
                throw new Error(
                    `Corresponding report does not exist: ${updated.id}`
                );
            }

            return this.mergeKnownReports(current, updated);
        });

        // Perform bulk update on storage.
        return this.reportRepository.updateBulk(db, reportsToUpdate);
    };

    private mergeKnownReports = (
        current: TaskReport,
        updated: TaskReport
    ): TaskReport => {
        // Merge answers.
        const answers = this.mergeAnswers(current.answers, updated.answers);

        // Merge rest of report.
        // If the current report is newer than the updated one,
        // then use most of its fields, otherwise use the updated one's.
        return new Date(current.lastModifiedAt) >
            new Date(updated.lastModifiedAt)
            ? { ...current, answers }
            : { ...updated, answers };
    };

    private mergeAnswers = <T extends Answer>(
        currentAnswers: Record<string, T>,
        updatedAnswers: Record<string, T>
    ): Record<string, T> => {
        // By default, incude all current answers.
        const mergedRecords: Record<string, T> = { ...currentAnswers };

        // Go through each updated answer.
        for (const [updatedId, updated] of Object.entries(updatedAnswers)) {
            // Find corresponding current answer.
            const current = mergedRecords[updatedId];

            if (!current) {
                // No corresponding current answer - must be a new one - simply add it.
                mergedRecords[updatedId] = updated;
                continue;
            }

            // Compare current with updated - one one modified last wins.
            mergedRecords[updatedId] =
                new Date(current.lastModifiedAt) >
                new Date(updated.lastModifiedAt)
                    ? current
                    : updated;
        }

        return mergedRecords;
    };
}
