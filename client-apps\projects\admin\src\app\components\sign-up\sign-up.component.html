<div class="sign-up-container">
  <h2>Create an Account</h2>

  <form [formGroup]="signUpForm" (ngSubmit)="onSignUp()">
    <label>Email Address</label>
    <input formControlName="email" type="email" placeholder="Enter your email" />
    <div *ngIf="signUpForm.get('email')?.invalid && signUpForm.get('email')?.touched">
      Please enter a valid email.
    </div>

    <label>Password</label>
    <input formControlName="password" type="password" placeholder="Enter your password" />
    <div *ngIf="signUpForm.get('password')?.invalid && signUpForm.get('password')?.touched">
      Password is required.
    </div>

    <label>Confirm Password</label>
    <input formControlName="confirmPassword" type="password" placeholder="Confirm your password" />
    <div *ngIf="signUpForm.get('confirmPassword')?.value !== signUpForm.get('password')?.value">
      Passwords do not match.
    </div>

    <button type="submit" [disabled]="signUpForm.invalid">Sign Up</button>

    <div class="login">
      <p>Already a member? <a (click)="goToLogin()">Return to login</a></p>
    </div>
  </form>
</div>