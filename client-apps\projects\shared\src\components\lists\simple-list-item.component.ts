import { Component, Input, Output, EventEmitter } from '@angular/core';
import type { Icon } from '@shared/types/icon.type';
import { IconComponent } from '../icons/icon.component';

@Component({
    selector: 'app-simple-list-item',
    imports: [IconComponent],
    template: `
        <div
            class="flex flex-grow items-center p-4 cursor-pointer transition-colors duration-200 rounded-lg bg-gray-50 border-1 border-gray-200 shadow-md hover:shadow-lg hover:bg-gray-100"
            (click)="onClick()"
        >
            <div class="flex-shrink-0 mr-4">
                <div
                    class="w-10 h-10 rounded-full flex items-center justify-center"
                >
                    <app-icon
                        [type]="icon"
                        class="w-10 h-10 text-gray-600"
                    ></app-icon>
                </div>
            </div>
            <div class="flex-grow">
                <h3 class="text-lg font-medium text-gray-900">{{ title }}</h3>
                <p class="text-sm text-gray-500">{{ subtitle }}</p>
            </div>
        </div>
    `,
})
export class SimpleListItemComponent {
    @Input() icon: Icon = 'bolt';
    @Input() title: string = '';
    @Input() subtitle: string = '';

    @Output() itemClick = new EventEmitter<void>();

    onClick() {
        this.itemClick.emit();
    }
}
