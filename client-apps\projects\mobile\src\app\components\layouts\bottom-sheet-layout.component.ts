import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { BottomSheetComponent } from '@shared/components/bottom-sheet/bottom-sheet.component';
import { NavigationService } from '../../core/services/navigation.service';

@Component({
    selector: 'app-bottom-sheet-layout',
    imports: [BottomSheetComponent, RouterOutlet],
    templateUrl: './bottom-sheet-layout.component.html',
})
export class BottomSheetLayoutComponent {
    showBottomSheet = true;

    constructor(private readonly navigationService: NavigationService) {}

    onClose(): void {
        // Remove overlay route.
        this.showBottomSheet = false;
        this.navigationService.closeBottomSheet();
    }
}
