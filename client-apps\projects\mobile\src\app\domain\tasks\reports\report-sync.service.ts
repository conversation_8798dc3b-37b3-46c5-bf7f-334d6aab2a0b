import { Injectable } from '@angular/core';
import type { Nullable } from 'lib/types/common.no-deps';
import { ReportRepository } from './report.repository';
import { AbstractSyncService } from '../../../core/services/abstract-sync.service';
import { HeartbeatService } from '../../../core/services/heartbeat.service';
import { HttpClientService } from '../../../core/services/http-client.service';
import { AssignmentRepository } from '../assignments/assignment.repository';
import { AssignmentService } from '../assignments/assignment.service';
import { ReportService } from '../reports/report.service';

@Injectable({ providedIn: 'root' })
export class TaskReportSyncService extends AbstractSyncService {
    constructor(
        heartbeatService: HeartbeatService,
        private readonly assignmentRepository: AssignmentRepository,
        private readonly assignmentService: AssignmentService,
        private readonly httpClient: HttpClientService,
        private readonly reportRepository: ReportRepository,
        private readonly reportService: ReportService
    ) {
        super(heartbeatService);
    }

    override async sync(
        lastSyncTime: Nullable<string>
    ): Promise<Nullable<string>> {
        // Fetch assignments that need to be synced using, `lastSyncTime` as reference.
        const assignmentsToSend = await (lastSyncTime !== null
            ? this.assignmentRepository.fetchSince(lastSyncTime)
            : this.assignmentRepository.fetchAll());

        // Fetch reports that need to be synced, using `lastSyncTime` as reference.
        const reportsToSend = await (lastSyncTime !== null
            ? this.reportRepository.fetchSince(lastSyncTime)
            : this.reportRepository.fetchAll());

        try {
            // Call API to send and receive updates.
            const { assignments, requestTime, reports } =
                await (lastSyncTime !== null
                    ? this.httpClient.syncReports(
                          assignmentsToSend,
                          reportsToSend,
                          lastSyncTime
                      )
                    : this.httpClient.syncReports(
                          assignmentsToSend,
                          reportsToSend
                      ));

            if (assignments.length > 0) {
                // Save updated assginments from API server.
                await this.assignmentService.upsertAssignments(assignments);
            }

            if (reports.length > 0) {
                // Save updated reports from API server.
                await this.reportService.upsertReports(reports);
            }

            return assignments.length > 0 || reports.length > 0
                ? requestTime
                : null;
        } catch (err) {
            console.error(
                'Error syncing assignments and reports to server:',
                err
            );

            return null;
        }
    }
}
