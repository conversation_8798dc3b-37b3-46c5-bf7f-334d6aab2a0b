import type { Meta, StoryObj } from '@storybook/angular';
import { TaskerComponent } from './tasker.component';

const meta: Meta<TaskerComponent> = {
    title: 'Components/Tasker',
    component: TaskerComponent,
    tags: ['autodocs'],
    parameters: {
        layout: 'centered',
    },
};

export default meta;
type Story = StoryObj<TaskerComponent>;

export const TodoTabActive: Story = {
    args: {
        activeTab: 'todo',
        todoCount: 8,
    },
};

export const DoneTabActive: Story = {
    args: {
        activeTab: 'done',
        todoCount: 8,
    },
};

export const EmptyTodoList: Story = {
    args: {
        activeTab: 'todo',
        todoCount: 0,
    },
};
