import {
    type JwtHeader,
    type JwtPayload,
    type SigningKeyCallback,
    verify,
} from 'jsonwebtoken';
import jwksClient from 'jwks-rsa';

const azureTenant = process.env.AZURE_AD_TENANT_ID || 'common';
const azureAudience = process.env.AZURE_AD_CLIENT_ID;
const azureIssuer = `https://login.microsoftonline.com/${azureTenant}/v2.0`;
const azureJwksUri = `https://login.microsoftonline.com/${azureTenant}/discovery/v2.0/keys`;
const azureClient = jwksClient({
    jwksUri: azureJwksUri,
    cache: true,
    rateLimit: true,
});

function getKey(header: JwtHeader, callback: SigningKeyCallback): void {
    azureClient.getSigningKey(header.kid, function (err, key) {
        if (err || !key) {
            callback(err || new Error('Signing key not found'), undefined);
        } else {
            const signingKey = key.getPublicKey();
            callback(null, signingKey);
        }
    });
}

export function verifyAzureAdToken(idToken: string): Promise<JwtPayload> {
    return new Promise((resolve, reject) => {
        verify(
            idToken,
            getKey,
            {
                audience: azureAudience,
                issuer: azureIssuer,
                algorithms: ['RS256'],
            },
            (err, decoded) => {
                if (err) {
                    reject(err);
                    return;
                }
                if (decoded === undefined) {
                    reject('Decoded JWT payload is undefined');
                    return;
                }
                if (typeof decoded === 'string') {
                    reject(
                        `Expected JWT payload to be object, not string: ${decoded}`
                    );
                    return;
                }
                resolve(decoded);
            }
        );
    });
}
