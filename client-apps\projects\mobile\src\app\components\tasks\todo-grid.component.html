<app-loading *ngIf="isPreparingReport" [message]="'Preparing report...'"></app-loading>

<div aria-disabled="isPreparingReport" class="flex flex-col flex-grow space-y-8">
    <div>
        <h4 class="flex text-lg justify-center mx-4 mt-4">In progress</h4>
        <app-grid-layout>
            <div
                *ngFor="let report of reports | keyvalue"
                (click)="onClickReport(report.key)"
                class="h-full hover:cursor-pointer hover:shadow-lg hover:shadow-gray-400 shadow-md rounded-lg border-1 border-gray-200 overflow-hidden"
            >
                <app-task-card
                    [datetime]="report.value.lastModifiedAt"
                    [description]="report.value.label || ''"
                    [icon]="'bolt'"
                    [tags]="reportTags"
                    [title]="{ color: 'orange', text: 'In progress' }"            
                ></app-task-card>
            </div>
        </app-grid-layout>
    </div>

    <div>
        <h4 class="flex text-lg justify-center mx-4 mt-4">Ready to start</h4>
        <app-grid-layout>
            <div
                *ngFor="let assignment of assignments | keyvalue"
                (click)="onClickAssignment(assignment.value)"
                class="h-full hover:cursor-pointer hover:shadow-lg hover:shadow-gray-400 shadow-md rounded-lg border-1 border-gray-200 overflow-hidden"
            >
                <app-task-card
                    [datetime]="assignment.value.lastModifiedAt"
                    [description]="assignment.value.label || ''"
                    [icon]="'flag'"
                    [tags]="assignmentTags"
                    [title]="{ color: 'red', text: 'Todo' }"
                ></app-task-card>
            </div>
        </app-grid-layout>
    </div>
</div>