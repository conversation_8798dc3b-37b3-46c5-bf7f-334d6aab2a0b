<div class="forgot-password-container">
  <h2>Recover Your Password</h2>

  <form [formGroup]="forgotPasswordForm" (ngSubmit)="onRecover()">
    <label>Email Address</label>
    <input formControlName="email" type="email" placeholder="Enter your email" />
    <div *ngIf="forgotPasswordForm.get('email')?.invalid && forgotPasswordForm.get('email')?.touched">
      Please enter a valid email.
    </div>

    <button type="submit" [disabled]="forgotPasswordForm.invalid">Recover</button>
    <div class="back">
      <a href="#">Go back</a>
    </div>
  </form>
</div>