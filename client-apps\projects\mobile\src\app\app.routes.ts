import type { Routes } from '@angular/router';
import { MainLayoutComponent } from './components/layouts/main-layout.component';
import { ExecutionLayoutComponent } from './components/layouts/execution-layout.component';

export const routes: Routes = [
    {
        path: '',
        component: MainLayoutComponent,
        loadChildren: () =>
            import('./routes/main-screen.routes').then(
                (m) => m.MAIN_LAYOUT_ROUTES
            ),
    },
    {
        path: 'execution',
        component: ExecutionLayoutComponent,
        loadChildren: () =>
            import('./routes/execution-screen.routes').then(
                (m) => m.EXECUTION_LAYOUT_ROUTES
            ),
    },
    {
        path: 'bs',
        outlet: 'bottom-sheet',
        loadChildren: () =>
            import('./routes/bottom-sheet.routes').then(
                (m) => m.BOTTOM_SHEET_ROUTES
            ),
    },

    // Fallback routes
    { path: '**', redirectTo: '' }, // Default
];
