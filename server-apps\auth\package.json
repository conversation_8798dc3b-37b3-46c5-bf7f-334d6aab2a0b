{"name": "auth", "version": "0.0.1", "description": "", "main": "dist/index.js", "scripts": {"build": "webpack", "dev": "ts-node src/index.ts", "start": "node dist/bundle.js", "tsoa": "npx tsoa routes && npx tsoa spec"}, "dependencies": {"body-parser": "^1.20.3", "express": "^4.21.2", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "tsoa": "^6.6.0"}, "devDependencies": {"@types/express": "^5.0.0", "@types/node": "^22.10.2", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "typescript": "^5.7.2", "webpack": "^5.97.1", "webpack-cli": "^6.0.1", "webpack-node-externals": "^3.0.0"}}