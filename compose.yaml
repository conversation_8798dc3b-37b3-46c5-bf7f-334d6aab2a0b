services:
  # PostgreSQL Database
  db:
    image: postgres:17-alpine
    container_name: compose-db
    environment:
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-postgres}
      POSTGRES_DB: ${DB_NAME:-opexflow}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - backend
    restart: unless-stopped

  # API Server Backend
  api:
    build:
      context: ./
      dockerfile: ./server-apps/api/Dockerfile
    container_name: compose-api
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      DB_HOST: db
      DB_PORT: 5432
      DB_USER: ${DB_USER:-postgres}
      DB_PASSWORD: ${DB_PASSWORD:-postgres}
      DB_NAME: ${DB_NAME:-opexflow}
    depends_on:
      - db
    networks:
      - backend
      - frontend
    restart: unless-stopped
    command: >
      sh -c "
        echo 'Waiting for database to start...'
        while ! nc -z db 5432; do
          sleep 1
        done
        echo 'Database started!'

        npm run migrate-latest

        npm run start
      "

  # Mobile App Frontend
  mobile:
    build:
      context: ./
      dockerfile: ./client-apps/Dockerfile
    container_name: compose-mobile
    depends_on:
      - api
    networks:
      - frontend
    restart: unless-stopped

  # Reverse proxy so that everything runs through port 80
  reverse-proxy:
    image: nginx:alpine
    container_name: compose-reverse-proxy
    ports:
      - "80:80"
    volumes:
      - ./config/nginx/docker-compose.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - api
      - mobile
    networks:
      - frontend
    restart: unless-stopped
    # command: >
    #   sh -c "nginx -g 'daemon off;'"

networks:
  backend:
  frontend:

volumes:
  postgres_data:

