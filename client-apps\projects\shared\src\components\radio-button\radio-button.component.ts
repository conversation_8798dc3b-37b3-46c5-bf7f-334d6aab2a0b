import { Component, Input } from '@angular/core';

@Component({
    selector: 'app-radio-button',
    templateUrl: './radio-button.component.html',
    styleUrls: ['./radio-button.component.css'],
})
export class RadioButtonComponent {
    @Input() label: string = 'Yes';
    @Input() name: string = 'radioGroup';
    @Input() checked: boolean = false;

    onSelectionChange(value: boolean) {
        this.checked = value;
    }
}
