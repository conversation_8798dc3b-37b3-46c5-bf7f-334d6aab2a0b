import { Meta, StoryObj } from '@storybook/angular';
import { InteractiveButtonComponent } from './interactive-button.component';

const meta: Meta<InteractiveButtonComponent> = {
    title: 'Components/Interactive Button',
    component: InteractiveButtonComponent,
    tags: ['autodocs'],
    argTypes: {
        size: {
            control: 'select',
            options: ['small', 'medium', 'large'],
        },
        color: {
            control: 'color',
        },
        label: {
            control: 'text',
        },
        buttonClick: {
            action: 'clicked',
        },
    },
};

export default meta;
type Story = StoryObj<InteractiveButtonComponent>;

export const Primary: Story = {
    args: {
        label: 'Primary Button',
        size: 'medium',
        color: '#3498db',
    },
};

export const Small: Story = {
    args: {
        label: 'Small Button',
        size: 'small',
        color: '#2ecc71',
    },
};

export const Large: Story = {
    args: {
        label: 'Large Button',
        size: 'large',
        color: '#e74c3c',
    },
};
