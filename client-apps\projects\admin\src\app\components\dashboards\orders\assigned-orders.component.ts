import { Component, OnInit } from '@angular/core';
import type { Order } from 'lib/types/order.no-deps';
import { OrderService } from '../../../services/order.service';
import { OrderItem, OrdersGridComponent } from './orders-grid.component';

@Component({
    selector: 'app-assigned-orders',
    imports: [OrdersGridComponent],
    template: ` <app-orders-grid [orders]="orders"></app-orders-grid> `,
    styles: [
        `
            :host {
                display: flex;
                flex-direction: column;
                flex-grow: 1;
            }
        `,
    ],
})
export class AssignedOrdersComponent implements OnInit {
    ordersMap: Map<Order['id'], OrderItem> = new Map();

    constructor(private readonly orderService: OrderService) {}

    get orders(): OrderItem[] {
        return Array.from(this.ordersMap.values());
    }

    ngOnInit(): void {
        this.orderService.orders$.subscribe((orders) =>
            orders.forEach((order) =>
                this.ordersMap.set(order.id, {
                    datetime: order.createdAt,
                    description: `${order.customer.label} - ${order.requirements}`,
                    icon: 'workflow',
                    id: order.id,
                    tags: [{ text: order.origin.label }],
                    title: { color: 'orange', text: 'Needs assignment' },
                    hint: order.origin.label,
                })
            )
        );
    }
}
