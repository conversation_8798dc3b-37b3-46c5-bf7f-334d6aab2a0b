import { CommonModule } from '@angular/common';
import {
    Component,
    EventEmitter,
    Input,
    Output,
    booleanAttribute,
    inject,
} from '@angular/core';
import { DropdownMenuComponent } from './dropdown-menu.component';

@Component({
    selector: 'app-dropdown-menu-item',
    standalone: true,
    imports: [CommonModule],
    template: `
        <button
            class="block w-full text-left text-sm text-gray-700 cursor-pointer hover:bg-gray-100 hover:text-gray-900"
            role="menuitem"
            [disabled]="disabled"
            (click)="handleClick($event)"
        >
            <ng-content></ng-content>
        </button>
    `,
    styles: [],
})
export class DropdownMenuItemComponent {
    @Output() onClick = new EventEmitter<MouseEvent>();
    @Input({ transform: booleanAttribute }) disabled = false;

    private dropdownMenu = inject(DropdownMenuComponent, { optional: true });

    handleClick(event: MouseEvent): void {
        this.onClick.emit(event);

        // Close the parent dropdown menu if closeOnItemClick is true
        if (this.dropdownMenu?.closeOnItemClick) {
            this.dropdownMenu.closeDropdown();
        }

        event.stopPropagation();
    }
}
