import type { Optional, UUID } from 'lib/types/common.no-deps';
import type { Message } from 'lib/types/message.no-deps';
import type { Order, OrdersFetchDTO } from 'lib/types/order.no-deps';
import type { Transaction } from '../../../database/database.types';
import { WsChannelSubObserver } from '../../../shared/ws-publisher';

export type OrderEvent = 'fetchedAllOrders' | 'orderCreated' | 'orderDeleted';

export interface CreateOneDTO
    extends Omit<Order, 'createdAt' | 'id' | 'status'> {
    status?: Order['status'];
}

export abstract class AbstractOrderService implements WsChannelSubObserver {
    abstract getChannel(): string;

    abstract onSub(
        pubFn: (message: Message<OrdersFetchDTO, OrderEvent>) => void
    ): void;

    abstract fetchAll(db: Transaction): Promise<Order[]>;

    abstract fetchById(db: Transaction, id: UUID): Promise<Optional<Order>>;

    abstract createOne(
        db: Transaction,
        dto: CreateOneDTO
    ): Promise<Optional<UUID>>;

    abstract deleteOne(db: Transaction, id: UUID): Promise<boolean>;
}
