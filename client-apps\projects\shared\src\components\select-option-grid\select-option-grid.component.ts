import { CommonModule } from '@angular/common';
import {
    Component,
    EventEmitter,
    Input,
    OnChanges,
    Output,
    SimpleChanges,
} from '@angular/core';

interface Option<T> {
    id: T;
    label: string;
}

@Component({
    selector: 'app-select-option-grid',
    imports: [CommonModule],
    templateUrl: './select-option-grid.component.html',
})
export class SelectOptionGridComponent<T> implements OnChanges {
    @Input({ required: true }) id!: string;
    @Input({ required: true }) options!: Array<Option<T>>;
    @Input() selectedOptions: T[] = [];
    @Input() type: 'checkbox' | 'radio' = 'radio';

    @Output() onToggleOption: EventEmitter<T> = new EventEmitter();

    private selectedOptionsSet!: Set<T>;

    ngOnChanges(changes: SimpleChanges): void {
        // `selectedOptions` can change, so we listen for changes
        // and update internal selected option set.
        const selectedOptions = changes['selectedOptions']?.currentValue;
        if (selectedOptions) {
            this.selectedOptionsSet = new Set(selectedOptions);
        }
    }

    trackBy(_: number, option: Option<T>): T {
        return option.id;
    }

    toggleOption(id: T): void {
        this.onToggleOption.emit(id);
    }

    isOptionSelected(id: T): boolean {
        return this.selectedOptionsSet.has(id);
    }
}
