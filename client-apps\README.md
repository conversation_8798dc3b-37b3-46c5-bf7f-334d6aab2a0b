# client-apps

This project was generated using [Angular CLI](https://github.com/angular/angular-cli) version 19.0.6.

The project is meant to be aggregator for multiple sub-projects. They can be found under `projects`. Each sub-project is it's own Angular application, as well as a common library.

Current sub-projects: `admin`, `mobile`.

## Admin application (sub-project)

The management application of our system. Using this, power-users can edit tasks, trainings, guides, schedules, users, as well as monitoring dashboards.

This application generally targets desktop devices.

### Mobile application (sub-project)

The execution application of our system. Factory workers interact with it in order to execute tasks, trainings, actions, as well as manage their own user profile.

This application generally targets mobile devices.

#### Shared library (sub-project)

A common library for all Angular applications. The component library as well as common utilities can be found here.

## Development server

To start a local development server, run:

```bash
npm run start <sub-project>
```

Once the server is running, open your browser and navigate to `http://localhost:4200/`. The application will automatically reload whenever you modify any of the source files.

## Building

To build the project run:

```bash
npm run build <sub-project>
```

This will compile your project and store the build artifacts in the `dist/<sub-project>` directory. By default, the production build optimizes your application for performance and speed.

## Running unit tests

To execute unit tests with the [Karma](https://karma-runner.github.io) test runner, use the following command:

```bash
npm run test <sub-project>
```

## Running end-to-end tests

For end-to-end (e2e) testing, run:

```bash
npm run e2e <sub-project>
```

Angular CLI does not come with an end-to-end testing framework by default. You can choose one that suits your needs.

## Additional Resources

For more information on using the Angular CLI, including detailed command references, visit the [Angular CLI Overview and Command Reference](https://angular.dev/tools/cli) page.
