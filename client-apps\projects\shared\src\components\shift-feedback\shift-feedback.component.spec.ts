import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ShiftFeedbackComponent } from './shift-feedback.component';

describe('ShiftFeedbackComponent', () => {
    let component: ShiftFeedbackComponent;
    let fixture: ComponentFixture<ShiftFeedbackComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ShiftFeedbackComponent],
        }).compileComponents();

        fixture = TestBed.createComponent(ShiftFeedbackComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
        expect(component.rating).toBe(0);
        expect(component.comments).toBe('');
    });

    it('should update rating when setRating is called', () => {
        component.setRating(5);
        expect(component.rating).toBe(5);
    });

    it('should update comments when updateComments is called', () => {
        const testComment = 'Test feedback';
        component.updateComments(testComment);
        expect(component.comments).toBe(testComment);
    });

    it('should clear form when resetForm is called', () => {
        component.setRating(5);
        component.updateComments('Test');
        component.resetForm();

        expect(component.rating).toBe(0);
        expect(component.comments).toBe('');
    });
});
