<div 
    *ngIf="isOpen"
    class="fixed inset-0 z-10 overflow-hidden"
    (click)="close()"
>
    <!-- Overlay -->
    <div class="absolute inset-0 bg-black opacity-50 transition-opacity"></div>

    <!-- Bottom Sheet Content -->
    <div 
        class="absolute bottom-0 left-0 right-0 bg-white rounded-t-2xl shadow-xl transform transition-all duration-300 ease-in-out max-h-[90vh] overflow-y-auto"
        (click)="stopPropagation($event)"
        @slideUpDown
    >

        <!-- Header -->
        <header class="flex p-4 bg-white border-b border-gray-200 sticky top-0 z-10">
            <h2 class="flex flex-grow text-xl font-medium justify-center text-gray-900 m-0">
                {{ title }}
            </h2>
            <button
                class="bg-transparent border-none cursor-pointer h-8 w-8 p-1 text-gray-500 hover:text-gray-900 transition-colors duration-200"
                (click)="close()"
                aria-label="Close bottom sheet"
            >
                <app-icon-close></app-icon-close>
            </button>
        </header>

        <!-- Content slot -->
        <ng-content></ng-content>
    </div>
</div>