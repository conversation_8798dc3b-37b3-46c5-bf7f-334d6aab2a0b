import type { Meta, StoryObj } from '@storybook/angular';
import { moduleMetadata } from '@storybook/angular';
import { FilterBarComponent } from './filter-bar.component';

const meta: Meta<FilterBarComponent> = {
    title: 'Components/FilterBar',
    component: FilterBarComponent,
    decorators: [
        moduleMetadata({
            imports: [FilterBarComponent],
        }),
    ],
    tags: ['autodocs'],
    argTypes: {
        searchChange: { action: 'searchChange' },
        filterClick: { action: 'filterClick' },
    },
};

export default meta;
type Story = StoryObj<FilterBarComponent>;

export const Default: Story = {
    args: {
        searchQuery: '',
    },
};

export const WithPrefilledSearch: Story = {
    args: {
        searchQuery: 'Test Query',
    },
};
