import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { EventType, Router, RouterOutlet } from '@angular/router';
import { TabContainerComponent } from '@shared/components/tab-container/tab-container.component';
import { ConstSet } from 'lib/util/set';
import { filter, type Subscription } from 'rxjs';
import { NavigationService } from '../../core/services/navigation.service';

const tabIds = ['done', 'todo'] as const;
const tabIdSet = new ConstSet(tabIds);

interface Tab {
    id: (typeof tabIds)[number];
    label: string;
}

@Component({
    selector: 'app-assignments',
    imports: [CommonModule, RouterOutlet, TabContainerComponent],
    templateUrl: './assignments.component.html',
    styles: [
        `
            :host {
                display: flex;
                overflow: hidden;
            }
        `,
    ],
})
export class AssignmentsComponent implements OnInit, OnDestroy {
    tabs: Tab[] = [
        { id: 'todo', label: 'TODO' },
        { id: 'done', label: 'DONE' },
    ];

    activeTab: Tab['id'] = 'todo';

    private routerUrlSubscription!: Subscription;

    constructor(
        private readonly router: Router,
        private readonly routeService: NavigationService
    ) {}

    ngOnInit(): void {
        this.subscribeToUrlChanges();
    }

    ngOnDestroy(): void {
        this.routerUrlSubscription.unsubscribe();
    }

    onTabChange(tabId: string): void {
        if (tabId !== 'todo' && tabId !== 'done') {
            throw new Error(`Unknown tabId: ${tabId}`);
        }

        this.routeService.routeToAssignmentsTab(tabId);
    }

    private subscribeToUrlChanges(): void {
        this.setActiveTabBasedOnUrl();

        this.routerUrlSubscription = this.router.events
            .pipe(filter((event) => event.type === EventType.NavigationEnd))
            .subscribe(this.setActiveTabBasedOnUrl);
    }

    private setActiveTabBasedOnUrl = (): void => {
        // We are only interested in a specific part of the full URL.
        const path = this.router.url.split(/^\?|(?=\()|\//g)[2];

        this.activeTab = tabIdSet.assertAndReturn(path);
    };
}
