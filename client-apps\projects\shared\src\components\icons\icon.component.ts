import { Component, Input } from '@angular/core';
import { BellIconComponent } from './bell.component';
import { BoltIconComponent } from './bolt.component';
import { CalendarIconComponent } from './calendar.component';
import { CheckmarkIconComponent } from './checkmark.component';
import { CloseIconComponent } from './close.components';
import { ExitIconComponent } from './exit.component';
import { FlagIconComponent } from './flag.component';
import { LeftChevronIconComponent } from './left-chevron.component';
import { PlusIconComponent } from './plus.component';
import { SearchIconComponent } from './search.component';
import { WorkflowIconComponent } from './workflow.component';
import type { Icon } from '../../types/icon.type';

@Component({
    selector: 'app-icon',
    template: `
        @switch (type) {
            @case ('bell') {
                <app-icon-bell></app-icon-bell>
            }
            @case ('bolt') {
                <app-icon-bolt></app-icon-bolt>
            }
            @case ('calendar') {
                <app-icon-calendar></app-icon-calendar>
            }
            @case ('checkmark') {
                <app-icon-checkmark></app-icon-checkmark>
            }
            @case ('close') {
                <app-icon-close></app-icon-close>
            }
            @case ('exit') {
                <app-icon-exit></app-icon-exit>
            }
            @case ('flag') {
                <app-icon-flag></app-icon-flag>
            }
            @case ('left-chevron') {
                <app-icon-left-chevron></app-icon-left-chevron>
            }
            @case ('plus') {
                <app-icon-plus></app-icon-plus>
            }
            @case ('search') {
                <app-icon-search></app-icon-search>
            }
            @case ('workflow') {
                <app-icon-workflow></app-icon-workflow>
            }
        }
    `,
    imports: [
        BellIconComponent,
        BoltIconComponent,
        CalendarIconComponent,
        CheckmarkIconComponent,
        CloseIconComponent,
        ExitIconComponent,
        FlagIconComponent,
        LeftChevronIconComponent,
        PlusIconComponent,
        SearchIconComponent,
        WorkflowIconComponent,
    ],
})
export class IconComponent {
    @Input({ required: true }) type!: Icon;
}
