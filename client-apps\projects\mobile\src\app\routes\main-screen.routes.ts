import type { Routes } from '@angular/router';

export const MAIN_LAYOUT_ROUTES: Routes = [
    {
        path: 'assignments',
        title: 'Assignments',
        loadComponent: () =>
            import('../components/tasks/assignments.component').then(
                (m) => m.AssignmentsComponent
            ),
        children: [
            {
                path: 'todo',
                title: 'Assignments | ToDo',
                loadComponent: () =>
                    import('../components/tasks/todo-grid.component').then(
                        (m) => m.TodoGridComponent
                    ),
            },
            {
                path: 'done',
                title: 'Assignments | Done',
                loadComponent: () =>
                    import('../components/tasks/done-grid.component').then(
                        (m) => m.DoneGridComponent
                    ),
            },
        ],
    },
    {
        path: 'search',
        title: 'Search',
        loadComponent: () =>
            import('../components/search/search.component').then(
                (m) => m.SearchComponent
            ),
    },
    {
        path: 'actions',
        title: 'Actions',
        loadComponent: () =>
            import('../components/actions/actions.component').then(
                (m) => m.ActionsComponent
            ),
    },
    {
        path: 'notifications',
        title: 'Notifications',
        loadComponent: () =>
            import('../components/notifications/notifications.component').then(
                (m) => m.NotificationsComponent
            ),
    },
    { path: '', redirectTo: 'assignments/todo', pathMatch: 'full' }, // Default
];
