import {
    animate,
    state,
    style,
    transition,
    trigger,
} from '@angular/animations';
import { CommonModule } from '@angular/common';
import {
    Component,
    EventEmitter,
    HostListener,
    Input,
    Output,
} from '@angular/core';
import { CloseIconComponent } from '../icons/close.components';

@Component({
    selector: 'app-bottom-sheet',
    imports: [CloseIconComponent, CommonModule],
    templateUrl: './bottom-sheet.component.html',
    animations: [
        trigger('slideUpDown', [
            state('void', style({ transform: 'translateY(100%)' })),
            transition(':enter', [
                animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)'),
            ]),
            transition(':leave', [
                animate('250ms cubic-bezier(0.4, 0, 0.2, 1)'),
            ]),
        ]),
    ],
})
export class BottomSheetComponent {
    @Input() isOpen = false;
    @Input() title = '';

    @Output() closed = new EventEmitter<void>();

    @HostListener('document:keydown.escape', ['$event'])
    handleEscapeKey(event: KeyboardEvent) {
        if (this.isOpen) {
            this.close();
            event.preventDefault();
        }
    }

    close() {
        this.closed.emit();
    }

    stopPropagation(event: Event) {
        event.stopPropagation();
    }
}
