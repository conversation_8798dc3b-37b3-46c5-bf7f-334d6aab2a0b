import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';

@Component({
    selector: 'app-grid-layout-item',
    standalone: true,
    template: `
        <div class="grid-item" [ngClass]="itemClasses">
            <ng-content></ng-content>
        </div>
    `,
    styles: [
        `
            .grid-item {
                height: 100%;
                width: 100%;
            }
        `,
    ],
    imports: [CommonModule],
})
export class GridLayoutItemComponent implements OnInit {
    @Input() colSpan: number = 1;
    @Input() rowSpan: number = 1;
    @Input() itemClasses: string = '';

    ngOnInit(): void {
        if (this.colSpan > 1) {
            this.itemClasses += ` col-span-${Math.min(this.colSpan, 12)}`;
        }

        if (this.rowSpan > 1) {
            this.itemClasses += ` row-span-${Math.min(this.rowSpan, 6)}`;
        }
    }
}
