import { Meta, StoryObj } from '@storybook/angular';
import { LoadingAlertComponent } from './loading-alert.component';

const meta: Meta<LoadingAlertComponent> = {
    title: 'Components/LoadingAlert',
    component: LoadingAlertComponent,
    tags: ['autodocs'],
    argTypes: {
        message: { control: 'text' },
        duration: { control: 'number' },
        showCloseButton: { control: 'boolean' },
        backgroundColor: { control: 'color' },
        textColor: { control: 'color' },
        dismiss: { action: 'dismissed' },
    },
};

export default meta;
type Story = StoryObj<LoadingAlertComponent>;

export const Default: Story = {
    args: {
        message: 'Processing...',
        duration: 5000,
        showCloseButton: true,
        backgroundColor: '#333',
        textColor: '#fff',
    },
};

export const NoCloseButton: Story = {
    args: {
        message: 'Loading data...',
        showCloseButton: false,
        duration: 3000,
    },
};

export const CustomColors: Story = {
    args: {
        message: 'Saving changes...',
        backgroundColor: '#2196f3',
        textColor: '#ffffff',
        duration: 0, // Won't auto-dismiss
    },
};

export const LongMessage: Story = {
    args: {
        message:
            'This is a longer loading message that demonstrates how the alert handles more text content...',
        duration: 8000,
    },
};
