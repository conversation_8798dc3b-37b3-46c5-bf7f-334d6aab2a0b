.bottom-nav {
  position: fixed;
  bottom: 0;
  width: 100%;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 48px;
  box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.1);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #757575;
  /* darker gray for better contrast */
  font-size: 14px;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  min-width: 64px;
  max-width: 80px;
  flex: 0 1 auto;
  margin: 0;
}

.nav-item:nth-child(2) {
  margin-right: 40px;
  /* Space for FAB button */
}

.nav-item:nth-child(3) {
  margin-left: 40px;
  /* Space for FAB button */
}

.nav-item:hover {
  color: #1a75ff;
}

.nav-item:active {
  transform: translateY(2px);
  opacity: 0.8;
  color: #0052cc;
  /* darker blue when pressed */
}

.icon {
  font-size: 24px;
  position: relative;
  margin-bottom: 6px;
}


.fab {
  position: absolute;
  top: -32px;
  /* Adjust for taller navbar */
  left: 50%;
  transform: translateX(-50%);
  background: blue;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 24px;
  font-weight: bold;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.2s ease;
  -webkit-tap-highlight-color: transparent;
}

.fab:hover {
  background: #1a75ff;
}

.fab:active {
  transform: translateX(-50%) translateY(2px);
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.2);
  opacity: 0.9;
}

.badge {
  position: absolute;
  top: -5px;
  right: -10px;
  background: red;
  color: white;
  font-size: 12px;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Add touch feedback for mobile */
@media (hover: none) {
  .nav-item:hover {
    color: #757575;
  }

  .nav-item:active {
    color: #0052cc;
    /* show blue only when tapped */
  }

  .fab:hover {
    background: blue;
  }
}

@media (max-width: 360px) {
  .bottom-nav {
    padding: 16px 24px;
  }

  .nav-item {
    min-width: 48px;
    max-width: 64px;
  }

  .nav-item:nth-child(2) {
    margin-right: 32px;
  }

  .nav-item:nth-child(3) {
    margin-left: 32px;
  }

  .fab {
    width: 32px;
    height: 32px;
    font-size: 18px;
    top: -24px;
  }
}