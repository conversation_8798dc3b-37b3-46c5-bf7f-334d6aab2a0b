import bodyParser from 'body-parser';
import * as express from 'express';
import helmet from 'helmet';
import { RegisterRoutes } from '../dist/routes';

const app: express.Application = express.default();
const port = 3000;

app.get('/', (_: express.Request, res: express.Response) => {
    res.send('Express + Typescript server');
});

// Middleware
app.use(helmet());
app.use(bodyParser.json());

// Register TSOA routes.
RegisterRoutes(app);

// Start web server.
app.listen(port, () => {
    console.log(`[server]: Server is running at http://localhost:${port}`);
});
