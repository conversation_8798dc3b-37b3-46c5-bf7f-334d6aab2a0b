import type { UUID } from 'lib/types/common.no-deps';
import type { TaskReport } from 'lib/types/task.no-deps';
import type { Transaction } from '../../../../database/database.types';

export abstract class AbstractTaskReportRepository {
    abstract fetchAll(
        db: Transaction,
        opts: { excludeIds?: UUID[]; since?: string }
    ): Promise<TaskReport[]>;

    abstract fetchByIds(db: Transaction, ids: UUID[]): Promise<TaskReport[]>;

    abstract fetchByIdsOrSince(
        db: Transaction,
        ids: UUID[],
        since?: string
    ): Promise<TaskReport[]>;

    abstract createBulk(
        db: Transaction,
        assignments: TaskReport[]
    ): Promise<UUID[]>;

    abstract updateBulk(
        db: Transaction,
        assignments: TaskReport[]
    ): Promise<UUID[]>;
}
