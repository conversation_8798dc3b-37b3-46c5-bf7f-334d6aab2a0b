// src/app/forgot-password/forgot-password.component.ts
import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
    ReactiveFormsModule,
    FormBuilder,
    FormGroup,
    Validators,
} from '@angular/forms';

@Component({
    standalone: true,
    selector: 'app-forgot-password',
    imports: [CommonModule, ReactiveFormsModule],
    templateUrl: './forgot-password.component.html',
    styleUrls: ['./forgot-password.component.css'],
})
export class ForgotPasswordComponent {
    forgotPasswordForm: FormGroup;

    constructor(private fb: FormBuilder) {
        this.forgotPasswordForm = this.fb.group({
            email: ['', [Validators.required, Validators.email]],
        });
    }

    onRecover(): void {
        if (this.forgotPasswordForm.valid) {
            console.log(
                'Recover password for:',
                this.forgotPasswordForm.value.email
            );
            // Handle the password recovery flow (API call, etc.)
        }
    }
}
