import type { Optional, UUID } from 'lib/types/common.no-deps';
import type {
    TaskAssignment,
    TaskAssignmentStatus,
    TaskMembers,
} from 'lib/types/task.no-deps';
import type { Transaction } from '../../../../database/database.types';

export interface CreateOneDTO
    extends Pick<
        TaskAssignment,
        'flow' | 'metadata' | 'templateId' | 'templateVersion'
    > {
    assignees?: TaskMembers;
    label?: string;
}

export interface UpdateOneDTO extends Partial<CreateOneDTO> {
    status?: TaskAssignmentStatus;
}

export abstract class AbstractTaskAssignmentRepository {
    abstract fetchAllOrSince(
        db: Transaction,
        since?: string
    ): Promise<TaskAssignment[]>;

    abstract fetchById(
        db: Transaction,
        id: UUID
    ): Promise<Optional<TaskAssignment>>;

    abstract fetchByIds(
        db: Transaction,
        ids: UUID[]
    ): Promise<TaskAssignment[]>;

    abstract fetchByIdsOrSince(
        db: Transaction,
        ids: UUID[],
        since?: string
    ): Promise<TaskAssignment[]>;

    abstract createOne(
        db: Transaction,
        userId: number,
        dto: CreateOneDTO
    ): Promise<UUID>;

    abstract createBulk(
        db: Transaction,
        assignments: TaskAssignment[]
    ): Promise<UUID[]>;

    abstract updateOne(
        db: Transaction,
        id: UUID,
        userId: number,
        dto: UpdateOneDTO
    ): Promise<boolean>;

    abstract updateBulk(
        db: Transaction,
        assignments: TaskAssignment[]
    ): Promise<UUID[]>;

    abstract deleteOne(
        db: Transaction,
        userId: number,
        id: UUID
    ): Promise<boolean>;
}
