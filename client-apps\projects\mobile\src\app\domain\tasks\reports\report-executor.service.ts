import { Injectable } from '@angular/core';
import { ReportExecutionService } from '@shared/tokens/report-execution.token';
import type { UUID } from 'lib/types/common.no-deps';
import type { Answer, AnswerValue, TaskReport } from 'lib/types/task.no-deps';
import { generateUUID } from 'lib/util/uuid';
import { BehaviorSubject, filter, map, type Observable } from 'rxjs';
import { toElementPathString } from '../../../core/utils/element.util';
import { UserService } from '../../users/user.service';
import { ReportRepository } from './report.repository';

@Injectable()
export class ReportExecutorService implements ReportExecutionService {
    private answers: TaskReport['answers'] = {};
    private currentUserId!: number;
    private reportId!: UUID;

    private answersSubject = new BehaviorSubject<TaskReport['answers']>(
        this.answers
    );

    private answers$ = this.answersSubject.asObservable();

    constructor(
        private readonly reportRepository: ReportRepository,
        private readonly userService: UserService
    ) {}

    /**
     * Loads report answers into answer map.
     */
    async init(reportId: UUID): Promise<void> {
        this.reportId = reportId;

        // Fetch current answer from storage.
        const [answers, currentUserId] = await Promise.all([
            this.fetchReportAnswers(reportId),
            this.userService.getCurrentUserId(),
        ]);

        this.answers = answers;
        this.currentUserId = currentUserId;

        this.answersSubject.next(answers);
    }

    getAnswerValue(elementPath: UUID[]): Observable<AnswerValue> {
        const pathStr = toElementPathString(elementPath);
        return this.answers$.pipe(
            filter((answers) => !!answers[pathStr]),
            map((answers) => answers[pathStr].value)
        );
    }

    async setAnswerValue(
        elementPath: UUID[],
        value: AnswerValue
    ): Promise<void> {
        const pathStr = toElementPathString(elementPath);

        let answer = this.answers[pathStr];

        // Create a new answer if it doesn't exist.
        if (!answer) {
            answer = this.createAnswer(value);

            this.answers[pathStr] = answer;
        } else {
            answer.lastModifiedAt = new Date().toISOString();
            answer.lastModifiedBy = this.currentUserId;
            answer.value = value;
        }

        await this.reportRepository.updateAnswers(
            this.reportId,
            this.answers,
            this.currentUserId
        );

        const nextAnswer: TaskReport['answers'] = {};
        nextAnswer[pathStr] = answer;
        this.answersSubject.next(nextAnswer);
    }

    private fetchReportAnswers = async (
        reportId: UUID
    ): Promise<TaskReport['answers']> => {
        const reportOpt = await this.reportRepository.fetchById(reportId);
        const report = reportOpt.orElseThrow(
            () => new Error(`Expected report to exist: ${reportId}`)
        );

        return report.answers;
    };

    private createAnswer = (value: AnswerValue): Answer => {
        const now = new Date().toISOString();
        switch (value.type) {
            case 'number':
                return {
                    createdAt: now,
                    createdBy: this.currentUserId,
                    id: generateUUID(),
                    lastModifiedAt: now,
                    lastModifiedBy: this.currentUserId,
                    value,
                };
            case 'numberArray':
                return {
                    createdAt: now,
                    createdBy: this.currentUserId,
                    id: generateUUID(),
                    lastModifiedAt: now,
                    lastModifiedBy: this.currentUserId,
                    value,
                };
            case 'text':
                return {
                    createdAt: now,
                    createdBy: this.currentUserId,
                    id: generateUUID(),
                    lastModifiedAt: now,
                    lastModifiedBy: this.currentUserId,
                    value,
                };
        }
    };
}
