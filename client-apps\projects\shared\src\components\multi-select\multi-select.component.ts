import {
    Component,
    EventEmitter,
    Input,
    Output,
    TemplateRef,
    ContentChild,
    OnChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';

export interface SelectableItem {
    id: string;
    label: string;
}

@Component({
    selector: 'app-multi-select',
    imports: [CommonModule],
    templateUrl: './multi-select.component.html',
})
export class MultiSelectComponent<I extends Component, S extends Component>
    implements OnChanges
{
    @Input({ required: true }) title: string = 'Select items';
    @Input() items: SelectableItem[] = [];
    @Input() itemsPerPage: number = 10;
    @Input() showPagination: boolean = true;

    @Output() selectionChange = new EventEmitter<SelectableItem[]>();
    @Output() pageChange = new EventEmitter<number>();

    // Template reference for custom item rendering
    @ContentChild('itemTemplate') itemTemplateRef?: TemplateRef<I>;

    // Template reference for custom selected item rendering
    @ContentChild('selectedItemTemplate')
    selectedItemTemplateRef?: TemplateRef<S>;

    selectedItems: SelectableItem[] = [];
    isOpen = false;
    searchTerm = '';
    currentPage = 1;
    totalPages = 1;

    ngOnChanges(): void {
        this.calculateTotalPages();
    }

    get filteredItems(): SelectableItem[] {
        if (!this.searchTerm) {
            return this.items;
        }

        const searchTermLowerCase = this.searchTerm.toLowerCase();
        return this.items.filter((item) =>
            item.label.toLowerCase().includes(searchTermLowerCase)
        );
    }

    get paginatedItems(): SelectableItem[] {
        this.calculateTotalPages();

        if (!this.showPagination) {
            return this.filteredItems;
        }

        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        return this.filteredItems.slice(
            startIndex,
            startIndex + this.itemsPerPage
        );
    }

    calculateTotalPages(): void {
        this.totalPages =
            Math.ceil(this.filteredItems.length / this.itemsPerPage) || 1;

        // Reset to first page if current page exceeds total pages
        if (this.currentPage > this.totalPages) {
            this.currentPage = 1;
        }
    }

    toggleSelection(item: SelectableItem): void {
        const index = this.selectedItems.findIndex((i) => i.id === item.id);
        if (index > -1) {
            this.selectedItems.splice(index, 1);
        } else {
            this.selectedItems.push(item);
        }
        this.selectionChange.emit(this.selectedItems);
    }

    isSelected(item: SelectableItem): boolean {
        return this.selectedItems.some((i) => i.id === item.id);
    }

    removeSelected(item: SelectableItem): void {
        this.toggleSelection(item);
    }

    toggleDropdown(): void {
        this.isOpen = !this.isOpen;
        if (this.isOpen) {
            this.searchTerm = '';
            this.currentPage = 1;
            this.calculateTotalPages();
        }
    }

    closeDropdown(): void {
        this.isOpen = false;
    }

    goToPage(page: number): void {
        if (page >= 1 && page <= this.totalPages) {
            this.currentPage = page;
            this.pageChange.emit(page);
        }
    }

    getContext(item: SelectableItem, selected: boolean) {
        return {
            $implicit: item,
            selected: selected,
            toggle: () => this.toggleSelection(item),
            remove: () => this.removeSelected(item),
        };
    }
}
