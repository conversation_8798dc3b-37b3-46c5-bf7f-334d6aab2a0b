import type { UUID } from './common.no-deps';

type OrderOriginType = 'integration' | 'user';

type OrderStatus =
    | 'created'
    | 'assigned'
    | 'inProgress'
    | 'awaitingReview'
    | 'done';

interface OrderCustomer {
    id: string;
    label: string;
}

interface OrderOrigin {
    id: string;
    label: string;
    type: OrderOriginType;
}

export interface Order {
    createdAt: string;
    customer: OrderCustomer;
    id: UUID;
    origin: OrderOrigin;
    requirements: string;
    status: OrderStatus;
}

export interface OrderChangeDTO {
    orderId: UUID;
}

export interface OrdersFetchDTO {
    orders: Order[];
}
