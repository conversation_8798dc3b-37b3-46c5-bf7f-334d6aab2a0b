// src/app/sign-up/sign-up.component.ts
import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import {
    ReactiveFormsModule,
    FormBuilder,
    FormGroup,
    Validators,
} from '@angular/forms';

@Component({
    standalone: true,
    selector: 'app-sign-up',
    imports: [CommonModule, ReactiveFormsModule],
    templateUrl: './sign-up.component.html',
    styleUrls: ['./sign-up.component.css'],
})
export class SignUpComponent {
    signUpForm: FormGroup;

    constructor(
        private fb: FormBuilder,
        private router: Router
    ) {
        this.signUpForm = this.fb.group({
            email: ['', [Validators.required, Validators.email]],
            password: ['', [Validators.required]],
            confirmPassword: ['', [Validators.required]],
        });
    }

    onSignUp(): void {
        const { password, confirmPassword } = this.signUpForm.value;

        if (this.signUpForm.valid && password === confirmPassword) {
            // Perform your sign-up logic here
            console.log('Sign Up Form Data:', this.signUpForm.value);
        } else {
            // Show an error, e.g. passwords don't match
        }
    }

    goToLogin(): void {
        this.router.navigate(['/login']);
    }
}
