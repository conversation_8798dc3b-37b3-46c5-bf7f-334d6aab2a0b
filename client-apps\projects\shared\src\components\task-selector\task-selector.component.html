<div class="container mx-auto p-4">
  <!-- Filter Section -->
  <div class="mb-6">
    <label for="filter" class="block text-sm font-medium text-gray-700 mb-1">Filter by Label</label>
    <input
      id="filter"
      type="text"
      [(ngModel)]="filterTerm"
      (ngModelChange)="updateFilter($event)"
      placeholder="Type to filter..."
      class="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
    />
  </div>

  <!-- Items Per Page Selector -->
  <div class="mb-4 flex items-center">
    <label for="itemsPerPage" class="mr-2 text-sm text-gray-700">Items per page:</label>
    <select
      id="itemsPerPage"
      [ngModel]="itemsPerPage()"
      (ngModelChange)="changeItemsPerPage($event)"
      class="p-1 border border-gray-300 rounded-md"
    >
      <option value="5">5</option>
      <option value="10">10</option>
      <option value="20">20</option>
      <option value="50">50</option>
    </select>
  </div>

  <!-- Items List -->
  <div class="bg-white shadow overflow-hidden sm:rounded-md mb-6">
    <ul class="divide-y divide-gray-200">
      @for (item of paginatedItems(); track item.id) {
        <li class="px-4 py-4 hover:bg-gray-50">
          <div class="flex items-center justify-between">
            <p class="text-sm font-medium text-indigo-600 truncate">{{ item.label }}</p>
            <span class="text-xs text-gray-500">ID: {{ item.id }}</span>
          </div>
        </li>
      } @empty {
        <li class="px-4 py-4 text-center text-gray-500">
          No items found matching your filter.
        </li>
      }
    </ul>
  </div>

  <!-- Pagination Controls -->
  @if (totalPages() > 1) {
    <div class="flex items-center justify-between border-t border-gray-200 px-4 py-3">
      <!-- Mobile view -->
      <div class="flex flex-1 justify-between sm:hidden">
        <button
          (click)="goToPage(currentPage() - 1)"
          [disabled]="currentPage() === 1"
          class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Previous
        </button>
        <button
          (click)="goToPage(currentPage() + 1)"
          [disabled]="currentPage() === totalPages()"
          class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Next
        </button>
      </div>
      
      <!-- Desktop view -->
      <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
        <div>
          <p class="text-sm text-gray-700">
            Showing <span class="font-medium">{{ (currentPage() - 1) * itemsPerPage() + 1 }}</span>
            to <span class="font-medium">{{ getPageSize() }}</span>
            of <span class="font-medium">{{ filteredItems().length }}</span> results
          </p>
        </div>
        <div>
          <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
            <button
              (click)="goToPage(currentPage() - 1)"
              [disabled]="currentPage() === 1"
              class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span class="sr-only">Previous</span>
              <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
              </svg>
            </button>
            
            @for (page of pageNumbers(); track page) {
              @if (page === -1) {
                <span class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300 focus:outline-offset-0">
                  ...
                </span>
              } @else {
                <button
                  (click)="goToPage(page)"
                  [class]="page === currentPage() 
                    ? 'relative z-10 inline-flex items-center bg-indigo-600 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600'
                    : 'relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0'"
                >
                  {{ page }}
                </button>
              }
            }
            
            <button
              (click)="goToPage(currentPage() + 1)"
              [disabled]="currentPage() === totalPages()"
              class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span class="sr-only">Next</span>
              <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
              </svg>
            </button>
          </nav>
        </div>
      </div>
    </div>
  }
</div>