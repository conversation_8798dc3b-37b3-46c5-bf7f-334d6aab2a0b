import { CommonModule } from '@angular/common';
import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { GridLayoutComponent } from '@shared/components/layouts/grid-layout.component';
import { TaskCardComponent } from '@shared/components/task-card/task-card.component';
import type { UUID } from 'lib/types/common.no-deps';
import type { TaskReport } from 'lib/types/task.no-deps';
import type { Subscription } from 'rxjs';
import { ReportService } from '../../domain/tasks/reports/report.service';

@Component({
    selector: 'app-done-grid',
    imports: [CommonModule, GridLayoutComponent, TaskCardComponent],
    templateUrl: './done-grid.component.html',
    styles: [
        `
            :host {
                display: flex;
                flex-grow: 1;
            }
        `,
    ],
})
export class DoneGridComponent implements OnDestroy, OnInit {
    reports: Map<UUID, TaskReport> = new Map();

    reportTags = [{ text: 'Leadership' }];

    private subscriptions: Subscription[] = [];

    constructor(private readonly reportService: ReportService) {}

    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions.
        this.subscriptions.forEach((subscription) =>
            subscription.unsubscribe()
        );
    }

    ngOnInit(): void {
        this.subscribeToReports();
    }

    onClickReport(_: UUID): void {
        alert('This report has been completed, nothing more to do for now');
    }

    private subscribeToReports = (): void => {
        const subscription = this.reportService
            .getComplete()
            .subscribe((reports) => {
                reports.forEach((report) => {
                    this.reports.set(report.id, report);
                });
            });
        this.subscriptions.push(subscription);
        this.reportService.fetchAll();
    };
}
