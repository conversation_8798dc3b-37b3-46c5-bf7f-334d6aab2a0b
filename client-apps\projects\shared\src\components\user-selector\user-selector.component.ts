// multi-select-generic.component.ts
import {
    Component,
    EventEmitter,
    Input,
    Output,
    forwardRef,
    TemplateRef,
    ContentChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';

export interface SelectableItem {
    id: string | number;
    [key: string]: any; // Allow additional properties
}

@Component({
    selector: 'app-user-selector',
    standalone: true,
    imports: [CommonModule, FormsModule],
    templateUrl: './user-selector.component.html',
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => UserSelectorComponent),
            multi: true,
        },
    ],
})
export class UserSelectorComponent implements ControlValueAccessor {
    @Input() items: SelectableItem[] = [];
    @Input() placeholder: string = 'Select items';
    @Input() itemsPerPage: number = 10;
    @Input() showPagination: boolean = true;
    @Output() selectionChange = new EventEmitter<SelectableItem[]>();
    @Output() pageChange = new EventEmitter<number>();

    // Template reference for custom item rendering
    @ContentChild('itemTemplate') itemTemplateRef?: TemplateRef<any>;
    // Template reference for custom selected item rendering
    @ContentChild('selectedItemTemplate')
    selectedItemTemplateRef?: TemplateRef<any>;

    selectedItems: SelectableItem[] = [];
    isOpen = false;
    searchTerm = '';
    currentPage = 1;
    totalPages = 1;

    // ControlValueAccessor methods
    onChange: any = () => {};
    onTouched: any = () => {};

    ngOnChanges(): void {
        this.calculateTotalPages();
    }

    writeValue(value: SelectableItem[]): void {
        this.selectedItems = value || [];
    }

    registerOnChange(fn: any): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: any): void {
        this.onTouched = fn;
    }

    get filteredItems(): SelectableItem[] {
        let filtered = this.items;

        if (this.searchTerm) {
            filtered = filtered.filter((item) =>
                JSON.stringify(item)
                    .toLowerCase()
                    .includes(this.searchTerm.toLowerCase())
            );
        }

        return filtered;
    }

    get paginatedItems(): SelectableItem[] {
        this.calculateTotalPages();

        if (!this.showPagination) {
            return this.filteredItems;
        }

        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        return this.filteredItems.slice(
            startIndex,
            startIndex + this.itemsPerPage
        );
    }

    calculateTotalPages(): void {
        this.totalPages =
            Math.ceil(this.filteredItems.length / this.itemsPerPage) || 1;

        // Reset to first page if current page exceeds total pages
        if (this.currentPage > this.totalPages) {
            this.currentPage = 1;
        }
    }

    toggleSelection(item: SelectableItem): void {
        const index = this.selectedItems.findIndex((i) => i.id === item.id);
        if (index > -1) {
            this.selectedItems.splice(index, 1);
        } else {
            this.selectedItems.push(item);
        }
        this.onChange(this.selectedItems);
        this.onTouched();
        this.selectionChange.emit(this.selectedItems);
    }

    isSelected(item: SelectableItem): boolean {
        return this.selectedItems.some((i) => i.id === item.id);
    }

    removeSelected(item: SelectableItem): void {
        this.toggleSelection(item);
    }

    toggleDropdown(): void {
        this.isOpen = !this.isOpen;
        if (this.isOpen) {
            this.searchTerm = '';
            this.currentPage = 1;
            this.calculateTotalPages();
        }
    }

    closeDropdown(): void {
        this.isOpen = false;
    }

    goToPage(page: number): void {
        if (page >= 1 && page <= this.totalPages) {
            this.currentPage = page;
            this.pageChange.emit(page);
        }
    }

    getContext(item: SelectableItem, selected: boolean) {
        return {
            $implicit: item,
            selected: selected,
            toggle: () => this.toggleSelection(item),
            remove: () => this.removeSelected(item),
        };
    }
}

/**
 * 
 *     @Input() items: SelectableItem[] = [
        {
            id: '1',
            name: 'John Doe',
            type: 'user',
            avatar: 'https://i.pravatar.cc/150?img=1',
        },
        {
            id: '2',
            name: 'Jane Smith',
            type: 'user',
            avatar: 'https://i.pravatar.cc/150?img=2',
        },
        { id: '3', name: 'Developers', type: 'group' },
        { id: '4', name: 'Design Team', type: 'group' },
        { id: '5', name: 'Product Managers', type: 'group' },
        {
            id: '6',
            name: 'Alex Johnson',
            type: 'user',
            avatar: 'https://i.pravatar.cc/150?img=3',
        },
    ];

 */
