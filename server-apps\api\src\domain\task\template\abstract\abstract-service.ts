import type { Optional, UUID } from 'lib/types/common.no-deps';
import type { TaskTemplate, TaskTemplateStatus } from 'lib/types/task.no-deps';
import type { Transaction } from '../../../../database/database.types';

export interface CreateOneDTO
    extends Partial<
        Pick<TaskTemplate, 'approvalMethod' | 'approvers' | 'flow' | 'label'>
    > {}

export interface UpdateOneDTO extends CreateOneDTO {
    status?: TaskTemplateStatus;
}

export interface TemplateFetchAllResponse {
    requestTime: string; // ISO 8601 date-time string
    templates: TaskTemplate[];
}

export abstract class AbstractTaskTemplateService {
    abstract fetchAll(
        db: Transaction,
        since?: string
    ): Promise<TemplateFetchAllResponse>;

    abstract fetchById(
        db: Transaction,
        id: UUID
    ): Promise<Optional<TaskTemplate>>;

    abstract createOne(
        db: Transaction,
        userId: number,
        dto: CreateOneDTO
    ): Promise<UUID>;

    abstract updateOne(
        db: Transaction,
        id: UUID,
        userId: number,
        dto: UpdateOneDTO
    ): Promise<boolean>;

    abstract deleteOne(
        db: Transaction,
        id: UUID,
        userId: number
    ): Promise<boolean>;
}
