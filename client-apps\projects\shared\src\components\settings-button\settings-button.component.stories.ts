import type { Meta, StoryObj } from '@storybook/angular';
import { SettingsButtonComponent } from './settings-button.component';

const meta: Meta<SettingsButtonComponent> = {
    title: 'Components/SettingsButton',
    component: SettingsButtonComponent,
    tags: ['autodocs'],
    argTypes: {
        onOpenSettings: { action: 'settings opened' },
    },
};

export default meta;
type Story = StoryObj<SettingsButtonComponent>;

export const Default: Story = {
    args: {},
};

export const WithCustomStyles: Story = {
    args: {},
    parameters: {
        backgrounds: { default: 'dark' },
    },
};
