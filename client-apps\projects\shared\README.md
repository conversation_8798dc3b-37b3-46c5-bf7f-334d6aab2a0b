# client-apps/shared

This sub-project contains shared code between all application-type sub-projects.

## Directory structure
- `src/assets`: icons, images, static files
- `src/components`: shared components (component library)
- `src/styles`: shared CSS styles
- `src/tokens`: shared service interfaces (e.g. shared components use a service that is implemented by one of the applications)
- `src/utils`: utility functions, classes and types.
