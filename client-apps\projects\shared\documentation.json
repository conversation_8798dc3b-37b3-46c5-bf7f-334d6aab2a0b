{"pipes": [], "interfaces": [], "injectables": [], "guards": [], "interceptors": [], "classes": [], "directives": [], "components": [], "modules": [], "miscellaneous": {"variables": [{"name": "Checked", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/radio-button/radio-button.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        label: 'Checked Option',\n        name: 'checkedGroup',\n        checked: true,\n    },\n}"}, {"name": "CustomColors", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading-alert/loading-alert.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        message: 'Saving changes...',\n        backgroundColor: '#2196f3',\n        textColor: '#ffffff',\n        duration: 0, // Won't auto-dismiss\n    },\n}"}, {"name": "CustomMessage", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading/loading.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "", "defaultValue": "Template.bind({})"}, {"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/back-button/back-button.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n}"}, {"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/congratulations/congratulations.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n}"}, {"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/filter-bar/filter-bar.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        searchQuery: '',\n    },\n}"}, {"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading/loading.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "", "defaultValue": "Template.bind({})"}, {"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading-alert/loading-alert.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        message: 'Processing...',\n        duration: 5000,\n        showCloseButton: true,\n        backgroundColor: '#333',\n        textColor: '#fff',\n    },\n}"}, {"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/radio-button/radio-button.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        label: 'Default Option',\n        name: 'defaultGroup',\n        checked: false,\n    },\n}"}, {"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/settings-button/settings-button.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n}"}, {"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n}"}, {"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/shift-feedback/shift-feedback.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        feedback: {\n            safety: '',\n            quality: '',\n            equipment: '',\n            motivation: '',\n            notes: '',\n        },\n    },\n}"}, {"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/sign-off-form/sign-off-form.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n}"}, {"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/task-card/task-card.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        task: {\n            title: 'Leadership',\n            dueDate: '19 March 2024',\n            categories: ['Leadership', 'Maintenance'],\n            reportProcedure: 'Shift Changeover',\n            reportSubject: 'Packaging',\n        },\n    },\n}"}, {"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/toggle-buttons/toggle-buttons.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        currentScreen: 'screen-one',\n    },\n}"}, {"name": "Desktop", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/bottom-nav/bottom-nav.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    parameters: {\n        viewport: {\n            defaultViewport: 'desktop',\n        },\n    },\n}"}, {"name": "DoneTabActive", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/tasker/tasker.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        activeTab: 'done',\n        todoCount: 8,\n    },\n}"}, {"name": "EmptyTodoList", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/tasker/tasker.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        activeTab: 'todo',\n        todoCount: 0,\n    },\n}"}, {"name": "GroupExample", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/radio-button/radio-button.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    render: () => ({\n        template: `\n      <div style=\"display: flex; flex-direction: column; gap: 10px;\">\n        <app-radio-button label=\"Option 1\" name=\"group1\" [checked]=\"true\"></app-radio-button>\n        <app-radio-button label=\"Option 2\" name=\"group1\"></app-radio-button>\n        <app-radio-button label=\"Option 3\" name=\"group1\"></app-radio-button>\n      </div>\n    `,\n    }),\n}"}, {"name": "Large", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/interactive-button/interactive-button.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        label: 'Large Button',\n        size: 'large',\n        color: '#e74c3c',\n    },\n}"}, {"name": "LargeSpinner", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading/loading.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "", "defaultValue": "Template.bind({})"}, {"name": "LongMessage", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading-alert/loading-alert.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        message:\n            'This is a longer loading message that demonstrates how the alert handles more text content...',\n        duration: 8000,\n    },\n}"}, {"name": "LongTitle", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/task-card/task-card.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        task: {\n            title: 'Comprehensive Manufacturing Process Review and Documentation Update',\n            dueDate: '21 March 2024',\n            categories: ['Documentation', 'Process'],\n            reportProcedure: 'Monthly Review',\n            reportSubject: 'Manufacturing',\n        },\n    },\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/back-button/back-button.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<BackButtonComponent>", "defaultValue": "{\n    title: 'Components/BackButton',\n    component: BackButtonComponent,\n    tags: ['autodocs'],\n    render: (args: BackButtonComponent) => ({\n        props: {\n            ...args,\n        },\n    }),\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/bottom-nav/bottom-nav.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<BottomNavComponent>", "defaultValue": "{\n    title: 'Components/Bottom Navigation',\n    component: BottomNavComponent,\n    parameters: {\n        layout: 'fullscreen',\n    },\n    argTypes: {\n        items: [\n            { label: 'Tasks', icon: 'calendar-icon' },\n            { label: 'Search', icon: 'search-icon' },\n            { label: 'Actions', icon: 'actions-icon' },\n            {\n                label: 'Notifications',\n                icon: 'notifications-icon',\n                notificationCount: 7,\n            },\n        ],\n        notificationCount: {\n            control: { type: 'number' },\n        },\n    },\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/congratulations/congratulations.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "<PERSON>a<CongratulationsComponent>", "defaultValue": "{\n    title: 'Components/Congratulations',\n    component: CongratulationsComponent,\n    decorators: [\n        // Provide necessary imports for the component\n        storybookModuleMetadata({\n            imports: [MatIconModule, MatButtonModule],\n        }),\n    ],\n    // Add any parameters needed for the story\n    parameters: {\n        layout: 'centered',\n    },\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/filter-bar/filter-bar.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<FilterBarComponent>", "defaultValue": "{\n    title: 'Components/FilterBar',\n    component: FilterBarComponent,\n    decorators: [\n        moduleMetadata({\n            imports: [FilterBarComponent],\n        }),\n    ],\n    tags: ['autodocs'],\n    argTypes: {\n        searchChange: { action: 'searchChange' },\n        filterClick: { action: 'filterClick' },\n    },\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/interactive-button/interactive-button.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<InteractiveButtonComponent>", "defaultValue": "{\n    title: 'Components/Interactive Button',\n    component: InteractiveButtonComponent,\n    tags: ['autodocs'],\n    argTypes: {\n        size: {\n            control: 'select',\n            options: ['small', 'medium', 'large'],\n        },\n        color: {\n            control: 'color',\n        },\n        label: {\n            control: 'text',\n        },\n        buttonClick: {\n            action: 'clicked',\n        },\n    },\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading-alert/loading-alert.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<LoadingAlertComponent>", "defaultValue": "{\n    title: 'Components/LoadingAlert',\n    component: LoadingAlertComponent,\n    tags: ['autodocs'],\n    argTypes: {\n        message: { control: 'text' },\n        duration: { control: 'number' },\n        showCloseButton: { control: 'boolean' },\n        backgroundColor: { control: 'color' },\n        textColor: { control: 'color' },\n        dismiss: { action: 'dismissed' },\n    },\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/radio-button/radio-button.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<RadioButtonComponent>", "defaultValue": "{\n    title: 'Components/Radio Button',\n    component: RadioButtonComponent,\n    tags: ['autodocs'],\n    argTypes: {\n        label: {\n            control: 'text',\n            description: 'Label text for the radio button',\n        },\n        name: {\n            control: 'text',\n            description: 'Group name for the radio button',\n        },\n        checked: {\n            control: 'boolean',\n            description: 'Whether the radio button is checked',\n        },\n    },\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/settings-button/settings-button.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<SettingsButtonComponent>", "defaultValue": "{\n    title: 'Components/SettingsButton',\n    component: SettingsButtonComponent,\n    tags: ['autodocs'],\n    argTypes: {\n        onOpenSettings: { action: 'settings opened' },\n    },\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<ShiftChangeoverComponent>", "defaultValue": "{\n    title: 'Components/ShiftChangeover',\n    component: ShiftChangeoverComponent,\n    tags: ['autodocs'],\n    parameters: {\n        layout: 'centered',\n    },\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/shift-feedback/shift-feedback.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<ShiftFeedbackComponent>", "defaultValue": "{\n    title: 'Components/ShiftFeedback',\n    component: ShiftFeedbackComponent,\n    decorators: [\n        // Add necessary module imports for the component\n        (story) => ({\n            ...story,\n            moduleMetadata: {\n                imports: [CommonModule, FormsModule],\n            },\n        }),\n    ],\n    // Optional: Add parameters for styling or other configurations\n    parameters: {\n        layout: 'centered',\n    },\n    // Optional: Add component level argTypes if needed\n    argTypes: {\n        submitFeedback: { action: 'submitFeedback' },\n    },\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/sign-off-form/sign-off-form.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<SignOffFormComponent>", "defaultValue": "{\n    title: 'Components/SignOffForm',\n    component: SignOffFormComponent,\n    decorators: [\n        applicationConfig({\n            providers: [\n                importProvidersFrom(\n                    FormsModule,\n                    ReactiveFormsModule,\n                    SignaturePad\n                ),\n            ],\n        }),\n    ],\n    tags: ['autodocs'],\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/task-card/task-card.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<TaskCardComponent>", "defaultValue": "{\n    title: 'Components/TaskCard',\n    component: TaskCardComponent,\n    tags: ['autodocs'],\n    render: (args) => ({\n        props: args,\n    }),\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/tasker/tasker.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<TaskerComponent>", "defaultValue": "{\n    title: 'Components/Tasker',\n    component: TaskerComponent,\n    tags: ['autodocs'],\n    parameters: {\n        layout: 'centered',\n    },\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/toggle-buttons/toggle-buttons.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<ToggleButtonsComponent>", "defaultValue": "{\n    title: 'Components/Toggle Buttons',\n    component: ToggleButtonsComponent,\n    tags: ['autodocs'],\n    decorators: [\n        // Import necessary components for the story to work\n        (story) => ({\n            ...story,\n            moduleMetadata: {\n                imports: [\n                    ScreenOneComponent,\n                    ScreenTwoComponent,\n                    ScreenThreeComponent,\n                ],\n            },\n        }),\n    ],\n}"}, {"name": "Mobile", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/bottom-nav/bottom-nav.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    parameters: {\n        viewport: {\n            defaultViewport: 'mobile1',\n        },\n    },\n}"}, {"name": "Mobile", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n    parameters: {\n        viewport: {\n            defaultViewport: 'mobile1',\n        },\n    },\n    render: (args) => ({\n        props: args,\n        template: `\n      <div style=\"width: 320px\">\n        <app-shift-changeover></app-shift-changeover>\n      </div>\n    `,\n    }),\n}"}, {"name": "MobileView", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading/loading.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "", "defaultValue": "Template.bind({})"}, {"name": "NoCloseButton", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading-alert/loading-alert.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        message: 'Loading data...',\n        showCloseButton: false,\n        duration: 3000,\n    },\n}"}, {"name": "PreFilled", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/shift-feedback/shift-feedback.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        feedback: {\n            safety: 'Yes',\n            quality: 'Yes',\n            equipment: 'No',\n            motivation: 'Maybe',\n            notes: 'Equipment maintenance needed for station 3.',\n        },\n    },\n}"}, {"name": "preview", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/.storybook/preview.ts", "deprecated": false, "deprecationMessage": "", "type": "Preview", "defaultValue": "{\n    parameters: {\n        controls: {\n            matchers: {\n                color: /(background|color)$/i,\n                date: /Date$/i,\n            },\n        },\n    },\n}"}, {"name": "Primary", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/interactive-button/interactive-button.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        label: 'Primary Button',\n        size: 'medium',\n        color: '#3498db',\n    },\n}"}, {"name": "SingleCategory", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/task-card/task-card.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        task: {\n            title: 'Safety Check',\n            dueDate: '20 March 2024',\n            categories: ['Safety'],\n            reportProcedure: 'Daily Check',\n            reportSubject: 'Equipment',\n        },\n    },\n}"}, {"name": "Small", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/interactive-button/interactive-button.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        label: 'Small Button',\n        size: 'small',\n        color: '#2ecc71',\n    },\n}"}, {"name": "StartWithScreenThree", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/toggle-buttons/toggle-buttons.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        currentScreen: 'screen-three',\n    },\n}"}, {"name": "StartWithScreenTwo", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/toggle-buttons/toggle-buttons.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        currentScreen: 'screen-two',\n    },\n}"}, {"name": "Tablet", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n    parameters: {\n        viewport: {\n            defaultViewport: 'tablet',\n        },\n    },\n    render: (args) => ({\n        props: args,\n        template: `\n      <div style=\"width: 768px\">\n        <app-shift-changeover></app-shift-changeover>\n      </div>\n    `,\n    }),\n}"}, {"name": "TabletView", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading/loading.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "", "defaultValue": "Template.bind({})"}, {"name": "Template", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading/loading.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "StoryFn<LoadingComponent>", "defaultValue": "(args: any) => ({\n    props: args,\n})"}, {"name": "TodoTabActive", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/tasker/tasker.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        activeTab: 'todo',\n        todoCount: 8,\n    },\n}"}, {"name": "WithCustomClick", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/back-button/back-button.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n    render: (args) => ({\n        props: {\n            ...args,\n            onNavigateBack: () => alert('Custom back action'),\n        },\n    }),\n}"}, {"name": "WithCustomNotifications", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/bottom-nav/bottom-nav.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        notificationCount: 15,\n    },\n}"}, {"name": "WithCustomStyles", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/settings-button/settings-button.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n    parameters: {\n        backgrounds: { default: 'dark' },\n    },\n}"}, {"name": "WithCustomTitle", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n    render: (args) => ({\n        props: args,\n        template: `\n      <div style=\"width: 600px\">\n        <app-shift-changeover></app-shift-changeover>\n      </div>\n    `,\n    }),\n}"}, {"name": "WithPrefilledDate", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/sign-off-form/sign-off-form.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n    render: (args) => ({\n        props: {\n            ...args,\n            ngOnInit: function (this: SignOffFormComponent) {\n                this['form'].patchValue({\n                    date: '2024-01-01',\n                    time: '12:00',\n                });\n            },\n        },\n    }),\n}"}, {"name": "WithPrefilledSearch", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/filter-bar/filter-bar.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        searchQuery: 'Test Query',\n    },\n}"}], "functions": [], "typealiases": [{"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<BackButtonComponent>", "file": "projects/shared/src/components/back-button/back-button.component.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}, {"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<BottomNavComponent>", "file": "projects/shared/src/components/bottom-nav/bottom-nav.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}, {"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<CongratulationsComponent>", "file": "projects/shared/src/components/congratulations/congratulations.component.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}, {"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<FilterBarComponent>", "file": "projects/shared/src/components/filter-bar/filter-bar.component.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}, {"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<InteractiveButtonComponent>", "file": "projects/shared/src/components/interactive-button/interactive-button.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}, {"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<LoadingAlertComponent>", "file": "projects/shared/src/components/loading-alert/loading-alert.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}, {"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<RadioButtonComponent>", "file": "projects/shared/src/components/radio-button/radio-button.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}, {"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<SettingsButtonComponent>", "file": "projects/shared/src/components/settings-button/settings-button.component.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}, {"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<ShiftChangeoverComponent>", "file": "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}, {"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<ShiftFeedbackComponent>", "file": "projects/shared/src/components/shift-feedback/shift-feedback.component.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}, {"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<SignOffFormComponent>", "file": "projects/shared/src/components/sign-off-form/sign-off-form.component.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}, {"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<TaskCardComponent>", "file": "projects/shared/src/components/task-card/task-card.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}, {"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<TaskerComponent>", "file": "projects/shared/src/components/tasker/tasker.component.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}, {"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<ToggleButtonsComponent>", "file": "projects/shared/src/components/toggle-buttons/toggle-buttons.component.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}], "enumerations": [], "groupedVariables": {"projects/shared/src/components/radio-button/radio-button.stories.ts": [{"name": "Checked", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/radio-button/radio-button.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        label: 'Checked Option',\n        name: 'checkedGroup',\n        checked: true,\n    },\n}"}, {"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/radio-button/radio-button.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        label: 'Default Option',\n        name: 'defaultGroup',\n        checked: false,\n    },\n}"}, {"name": "GroupExample", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/radio-button/radio-button.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    render: () => ({\n        template: `\n      <div style=\"display: flex; flex-direction: column; gap: 10px;\">\n        <app-radio-button label=\"Option 1\" name=\"group1\" [checked]=\"true\"></app-radio-button>\n        <app-radio-button label=\"Option 2\" name=\"group1\"></app-radio-button>\n        <app-radio-button label=\"Option 3\" name=\"group1\"></app-radio-button>\n      </div>\n    `,\n    }),\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/radio-button/radio-button.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<RadioButtonComponent>", "defaultValue": "{\n    title: 'Components/Radio Button',\n    component: RadioButtonComponent,\n    tags: ['autodocs'],\n    argTypes: {\n        label: {\n            control: 'text',\n            description: 'Label text for the radio button',\n        },\n        name: {\n            control: 'text',\n            description: 'Group name for the radio button',\n        },\n        checked: {\n            control: 'boolean',\n            description: 'Whether the radio button is checked',\n        },\n    },\n}"}], "projects/shared/src/components/loading-alert/loading-alert.stories.ts": [{"name": "CustomColors", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading-alert/loading-alert.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        message: 'Saving changes...',\n        backgroundColor: '#2196f3',\n        textColor: '#ffffff',\n        duration: 0, // Won't auto-dismiss\n    },\n}"}, {"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading-alert/loading-alert.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        message: 'Processing...',\n        duration: 5000,\n        showCloseButton: true,\n        backgroundColor: '#333',\n        textColor: '#fff',\n    },\n}"}, {"name": "LongMessage", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading-alert/loading-alert.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        message:\n            'This is a longer loading message that demonstrates how the alert handles more text content...',\n        duration: 8000,\n    },\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading-alert/loading-alert.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<LoadingAlertComponent>", "defaultValue": "{\n    title: 'Components/LoadingAlert',\n    component: LoadingAlertComponent,\n    tags: ['autodocs'],\n    argTypes: {\n        message: { control: 'text' },\n        duration: { control: 'number' },\n        showCloseButton: { control: 'boolean' },\n        backgroundColor: { control: 'color' },\n        textColor: { control: 'color' },\n        dismiss: { action: 'dismissed' },\n    },\n}"}, {"name": "NoCloseButton", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading-alert/loading-alert.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        message: 'Loading data...',\n        showCloseButton: false,\n        duration: 3000,\n    },\n}"}], "projects/shared/src/components/loading/loading.stories.ts": [{"name": "CustomMessage", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading/loading.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "", "defaultValue": "Template.bind({})"}, {"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading/loading.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "", "defaultValue": "Template.bind({})"}, {"name": "LargeSpinner", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading/loading.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "", "defaultValue": "Template.bind({})"}, {"name": "MobileView", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading/loading.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "", "defaultValue": "Template.bind({})"}, {"name": "TabletView", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading/loading.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "", "defaultValue": "Template.bind({})"}, {"name": "Template", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/loading/loading.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "StoryFn<LoadingComponent>", "defaultValue": "(args: any) => ({\n    props: args,\n})"}], "projects/shared/src/components/back-button/back-button.component.stories.ts": [{"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/back-button/back-button.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/back-button/back-button.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<BackButtonComponent>", "defaultValue": "{\n    title: 'Components/BackButton',\n    component: BackButtonComponent,\n    tags: ['autodocs'],\n    render: (args: BackButtonComponent) => ({\n        props: {\n            ...args,\n        },\n    }),\n}"}, {"name": "WithCustomClick", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/back-button/back-button.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n    render: (args) => ({\n        props: {\n            ...args,\n            onNavigateBack: () => alert('Custom back action'),\n        },\n    }),\n}"}], "projects/shared/src/components/congratulations/congratulations.component.stories.ts": [{"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/congratulations/congratulations.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/congratulations/congratulations.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "<PERSON>a<CongratulationsComponent>", "defaultValue": "{\n    title: 'Components/Congratulations',\n    component: CongratulationsComponent,\n    decorators: [\n        // Provide necessary imports for the component\n        storybookModuleMetadata({\n            imports: [MatIconModule, MatButtonModule],\n        }),\n    ],\n    // Add any parameters needed for the story\n    parameters: {\n        layout: 'centered',\n    },\n}"}], "projects/shared/src/components/filter-bar/filter-bar.component.stories.ts": [{"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/filter-bar/filter-bar.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        searchQuery: '',\n    },\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/filter-bar/filter-bar.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<FilterBarComponent>", "defaultValue": "{\n    title: 'Components/FilterBar',\n    component: FilterBarComponent,\n    decorators: [\n        moduleMetadata({\n            imports: [FilterBarComponent],\n        }),\n    ],\n    tags: ['autodocs'],\n    argTypes: {\n        searchChange: { action: 'searchChange' },\n        filterClick: { action: 'filterClick' },\n    },\n}"}, {"name": "WithPrefilledSearch", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/filter-bar/filter-bar.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        searchQuery: 'Test Query',\n    },\n}"}], "projects/shared/src/components/settings-button/settings-button.component.stories.ts": [{"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/settings-button/settings-button.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/settings-button/settings-button.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<SettingsButtonComponent>", "defaultValue": "{\n    title: 'Components/SettingsButton',\n    component: SettingsButtonComponent,\n    tags: ['autodocs'],\n    argTypes: {\n        onOpenSettings: { action: 'settings opened' },\n    },\n}"}, {"name": "WithCustomStyles", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/settings-button/settings-button.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n    parameters: {\n        backgrounds: { default: 'dark' },\n    },\n}"}], "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts": [{"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<ShiftChangeoverComponent>", "defaultValue": "{\n    title: 'Components/ShiftChangeover',\n    component: ShiftChangeoverComponent,\n    tags: ['autodocs'],\n    parameters: {\n        layout: 'centered',\n    },\n}"}, {"name": "Mobile", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n    parameters: {\n        viewport: {\n            defaultViewport: 'mobile1',\n        },\n    },\n    render: (args) => ({\n        props: args,\n        template: `\n      <div style=\"width: 320px\">\n        <app-shift-changeover></app-shift-changeover>\n      </div>\n    `,\n    }),\n}"}, {"name": "Tablet", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n    parameters: {\n        viewport: {\n            defaultViewport: 'tablet',\n        },\n    },\n    render: (args) => ({\n        props: args,\n        template: `\n      <div style=\"width: 768px\">\n        <app-shift-changeover></app-shift-changeover>\n      </div>\n    `,\n    }),\n}"}, {"name": "WithCustomTitle", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n    render: (args) => ({\n        props: args,\n        template: `\n      <div style=\"width: 600px\">\n        <app-shift-changeover></app-shift-changeover>\n      </div>\n    `,\n    }),\n}"}], "projects/shared/src/components/shift-feedback/shift-feedback.component.stories.ts": [{"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/shift-feedback/shift-feedback.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        feedback: {\n            safety: '',\n            quality: '',\n            equipment: '',\n            motivation: '',\n            notes: '',\n        },\n    },\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/shift-feedback/shift-feedback.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<ShiftFeedbackComponent>", "defaultValue": "{\n    title: 'Components/ShiftFeedback',\n    component: ShiftFeedbackComponent,\n    decorators: [\n        // Add necessary module imports for the component\n        (story) => ({\n            ...story,\n            moduleMetadata: {\n                imports: [CommonModule, FormsModule],\n            },\n        }),\n    ],\n    // Optional: Add parameters for styling or other configurations\n    parameters: {\n        layout: 'centered',\n    },\n    // Optional: Add component level argTypes if needed\n    argTypes: {\n        submitFeedback: { action: 'submitFeedback' },\n    },\n}"}, {"name": "PreFilled", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/shift-feedback/shift-feedback.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        feedback: {\n            safety: 'Yes',\n            quality: 'Yes',\n            equipment: 'No',\n            motivation: 'Maybe',\n            notes: 'Equipment maintenance needed for station 3.',\n        },\n    },\n}"}], "projects/shared/src/components/sign-off-form/sign-off-form.component.stories.ts": [{"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/sign-off-form/sign-off-form.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/sign-off-form/sign-off-form.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<SignOffFormComponent>", "defaultValue": "{\n    title: 'Components/SignOffForm',\n    component: SignOffFormComponent,\n    decorators: [\n        applicationConfig({\n            providers: [\n                importProvidersFrom(\n                    FormsModule,\n                    ReactiveFormsModule,\n                    SignaturePad\n                ),\n            ],\n        }),\n    ],\n    tags: ['autodocs'],\n}"}, {"name": "WithPrefilledDate", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/sign-off-form/sign-off-form.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {},\n    render: (args) => ({\n        props: {\n            ...args,\n            ngOnInit: function (this: SignOffFormComponent) {\n                this['form'].patchValue({\n                    date: '2024-01-01',\n                    time: '12:00',\n                });\n            },\n        },\n    }),\n}"}], "projects/shared/src/components/task-card/task-card.stories.ts": [{"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/task-card/task-card.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        task: {\n            title: 'Leadership',\n            dueDate: '19 March 2024',\n            categories: ['Leadership', 'Maintenance'],\n            reportProcedure: 'Shift Changeover',\n            reportSubject: 'Packaging',\n        },\n    },\n}"}, {"name": "LongTitle", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/task-card/task-card.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        task: {\n            title: 'Comprehensive Manufacturing Process Review and Documentation Update',\n            dueDate: '21 March 2024',\n            categories: ['Documentation', 'Process'],\n            reportProcedure: 'Monthly Review',\n            reportSubject: 'Manufacturing',\n        },\n    },\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/task-card/task-card.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<TaskCardComponent>", "defaultValue": "{\n    title: 'Components/TaskCard',\n    component: TaskCardComponent,\n    tags: ['autodocs'],\n    render: (args) => ({\n        props: args,\n    }),\n}"}, {"name": "SingleCategory", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/task-card/task-card.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        task: {\n            title: 'Safety Check',\n            dueDate: '20 March 2024',\n            categories: ['Safety'],\n            reportProcedure: 'Daily Check',\n            reportSubject: 'Equipment',\n        },\n    },\n}"}], "projects/shared/src/components/toggle-buttons/toggle-buttons.component.stories.ts": [{"name": "<PERSON><PERSON><PERSON>", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/toggle-buttons/toggle-buttons.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        currentScreen: 'screen-one',\n    },\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/toggle-buttons/toggle-buttons.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<ToggleButtonsComponent>", "defaultValue": "{\n    title: 'Components/Toggle Buttons',\n    component: ToggleButtonsComponent,\n    tags: ['autodocs'],\n    decorators: [\n        // Import necessary components for the story to work\n        (story) => ({\n            ...story,\n            moduleMetadata: {\n                imports: [\n                    ScreenOneComponent,\n                    ScreenTwoComponent,\n                    ScreenThreeComponent,\n                ],\n            },\n        }),\n    ],\n}"}, {"name": "StartWithScreenThree", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/toggle-buttons/toggle-buttons.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        currentScreen: 'screen-three',\n    },\n}"}, {"name": "StartWithScreenTwo", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/toggle-buttons/toggle-buttons.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        currentScreen: 'screen-two',\n    },\n}"}], "projects/shared/src/components/bottom-nav/bottom-nav.stories.ts": [{"name": "Desktop", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/bottom-nav/bottom-nav.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    parameters: {\n        viewport: {\n            defaultViewport: 'desktop',\n        },\n    },\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/bottom-nav/bottom-nav.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<BottomNavComponent>", "defaultValue": "{\n    title: 'Components/Bottom Navigation',\n    component: BottomNavComponent,\n    parameters: {\n        layout: 'fullscreen',\n    },\n    argTypes: {\n        items: [\n            { label: 'Tasks', icon: 'calendar-icon' },\n            { label: 'Search', icon: 'search-icon' },\n            { label: 'Actions', icon: 'actions-icon' },\n            {\n                label: 'Notifications',\n                icon: 'notifications-icon',\n                notificationCount: 7,\n            },\n        ],\n        notificationCount: {\n            control: { type: 'number' },\n        },\n    },\n}"}, {"name": "Mobile", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/bottom-nav/bottom-nav.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    parameters: {\n        viewport: {\n            defaultViewport: 'mobile1',\n        },\n    },\n}"}, {"name": "WithCustomNotifications", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/bottom-nav/bottom-nav.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        notificationCount: 15,\n    },\n}"}], "projects/shared/src/components/tasker/tasker.component.stories.ts": [{"name": "DoneTabActive", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/tasker/tasker.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        activeTab: 'done',\n        todoCount: 8,\n    },\n}"}, {"name": "EmptyTodoList", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/tasker/tasker.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        activeTab: 'todo',\n        todoCount: 0,\n    },\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/tasker/tasker.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<TaskerComponent>", "defaultValue": "{\n    title: 'Components/Tasker',\n    component: TaskerComponent,\n    tags: ['autodocs'],\n    parameters: {\n        layout: 'centered',\n    },\n}"}, {"name": "TodoTabActive", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/tasker/tasker.component.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        activeTab: 'todo',\n        todoCount: 8,\n    },\n}"}], "projects/shared/src/components/interactive-button/interactive-button.stories.ts": [{"name": "Large", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/interactive-button/interactive-button.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        label: 'Large Button',\n        size: 'large',\n        color: '#e74c3c',\n    },\n}"}, {"name": "meta", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/interactive-button/interactive-button.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Meta<InteractiveButtonComponent>", "defaultValue": "{\n    title: 'Components/Interactive Button',\n    component: InteractiveButtonComponent,\n    tags: ['autodocs'],\n    argTypes: {\n        size: {\n            control: 'select',\n            options: ['small', 'medium', 'large'],\n        },\n        color: {\n            control: 'color',\n        },\n        label: {\n            control: 'text',\n        },\n        buttonClick: {\n            action: 'clicked',\n        },\n    },\n}"}, {"name": "Primary", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/interactive-button/interactive-button.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        label: 'Primary Button',\n        size: 'medium',\n        color: '#3498db',\n    },\n}"}, {"name": "Small", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/src/components/interactive-button/interactive-button.stories.ts", "deprecated": false, "deprecationMessage": "", "type": "Story", "defaultValue": "{\n    args: {\n        label: 'Small Button',\n        size: 'small',\n        color: '#2ecc71',\n    },\n}"}], "projects/shared/.storybook/preview.ts": [{"name": "preview", "ctype": "miscellaneous", "subtype": "variable", "file": "projects/shared/.storybook/preview.ts", "deprecated": false, "deprecationMessage": "", "type": "Preview", "defaultValue": "{\n    parameters: {\n        controls: {\n            matchers: {\n                color: /(background|color)$/i,\n                date: /Date$/i,\n            },\n        },\n    },\n}"}]}, "groupedFunctions": {}, "groupedEnumerations": {}, "groupedTypeAliases": {"projects/shared/src/components/back-button/back-button.component.stories.ts": [{"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<BackButtonComponent>", "file": "projects/shared/src/components/back-button/back-button.component.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}], "projects/shared/src/components/bottom-nav/bottom-nav.stories.ts": [{"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<BottomNavComponent>", "file": "projects/shared/src/components/bottom-nav/bottom-nav.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}], "projects/shared/src/components/congratulations/congratulations.component.stories.ts": [{"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<CongratulationsComponent>", "file": "projects/shared/src/components/congratulations/congratulations.component.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}], "projects/shared/src/components/filter-bar/filter-bar.component.stories.ts": [{"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<FilterBarComponent>", "file": "projects/shared/src/components/filter-bar/filter-bar.component.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}], "projects/shared/src/components/interactive-button/interactive-button.stories.ts": [{"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<InteractiveButtonComponent>", "file": "projects/shared/src/components/interactive-button/interactive-button.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}], "projects/shared/src/components/loading-alert/loading-alert.stories.ts": [{"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<LoadingAlertComponent>", "file": "projects/shared/src/components/loading-alert/loading-alert.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}], "projects/shared/src/components/radio-button/radio-button.stories.ts": [{"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<RadioButtonComponent>", "file": "projects/shared/src/components/radio-button/radio-button.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}], "projects/shared/src/components/settings-button/settings-button.component.stories.ts": [{"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<SettingsButtonComponent>", "file": "projects/shared/src/components/settings-button/settings-button.component.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}], "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts": [{"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<ShiftChangeoverComponent>", "file": "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}], "projects/shared/src/components/shift-feedback/shift-feedback.component.stories.ts": [{"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<ShiftFeedbackComponent>", "file": "projects/shared/src/components/shift-feedback/shift-feedback.component.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}], "projects/shared/src/components/sign-off-form/sign-off-form.component.stories.ts": [{"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<SignOffFormComponent>", "file": "projects/shared/src/components/sign-off-form/sign-off-form.component.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}], "projects/shared/src/components/task-card/task-card.stories.ts": [{"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<TaskCardComponent>", "file": "projects/shared/src/components/task-card/task-card.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}], "projects/shared/src/components/tasker/tasker.component.stories.ts": [{"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<TaskerComponent>", "file": "projects/shared/src/components/tasker/tasker.component.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}], "projects/shared/src/components/toggle-buttons/toggle-buttons.component.stories.ts": [{"name": "Story", "ctype": "miscellaneous", "subtype": "typealias", "rawtype": "StoryObj<ToggleButtonsComponent>", "file": "projects/shared/src/components/toggle-buttons/toggle-buttons.component.stories.ts", "deprecated": false, "deprecationMessage": "", "description": "", "kind": 183}]}}, "routes": [], "coverage": {"count": 0, "status": "low", "files": [{"filePath": "projects/shared/.storybook/preview.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "preview", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/back-button/back-button.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "<PERSON><PERSON><PERSON>", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/back-button/back-button.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "meta", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/back-button/back-button.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "WithCustomClick", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/back-button/back-button.component.stories.ts", "type": "type alias", "linktype": "miscellaneous", "linksubtype": "typealias", "name": "Story", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/bottom-nav/bottom-nav.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "Desktop", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/bottom-nav/bottom-nav.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "meta", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/bottom-nav/bottom-nav.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "Mobile", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/bottom-nav/bottom-nav.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "WithCustomNotifications", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/bottom-nav/bottom-nav.stories.ts", "type": "type alias", "linktype": "miscellaneous", "linksubtype": "typealias", "name": "Story", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/congratulations/congratulations.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "<PERSON><PERSON><PERSON>", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/congratulations/congratulations.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "meta", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/congratulations/congratulations.component.stories.ts", "type": "type alias", "linktype": "miscellaneous", "linksubtype": "typealias", "name": "Story", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/filter-bar/filter-bar.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "<PERSON><PERSON><PERSON>", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/filter-bar/filter-bar.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "meta", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/filter-bar/filter-bar.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "WithPrefilledSearch", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/filter-bar/filter-bar.component.stories.ts", "type": "type alias", "linktype": "miscellaneous", "linksubtype": "typealias", "name": "Story", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/interactive-button/interactive-button.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "Large", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/interactive-button/interactive-button.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "meta", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/interactive-button/interactive-button.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "Primary", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/interactive-button/interactive-button.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "Small", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/interactive-button/interactive-button.stories.ts", "type": "type alias", "linktype": "miscellaneous", "linksubtype": "typealias", "name": "Story", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/loading-alert/loading-alert.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "CustomColors", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/loading-alert/loading-alert.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "<PERSON><PERSON><PERSON>", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/loading-alert/loading-alert.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "LongMessage", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/loading-alert/loading-alert.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "meta", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/loading-alert/loading-alert.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "NoCloseButton", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/loading-alert/loading-alert.stories.ts", "type": "type alias", "linktype": "miscellaneous", "linksubtype": "typealias", "name": "Story", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/loading/loading.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "CustomMessage", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/loading/loading.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "<PERSON><PERSON><PERSON>", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/loading/loading.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "LargeSpinner", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/loading/loading.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "MobileView", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/loading/loading.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "TabletView", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/loading/loading.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "Template", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/radio-button/radio-button.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "Checked", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/radio-button/radio-button.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "<PERSON><PERSON><PERSON>", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/radio-button/radio-button.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "GroupExample", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/radio-button/radio-button.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "meta", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/radio-button/radio-button.stories.ts", "type": "type alias", "linktype": "miscellaneous", "linksubtype": "typealias", "name": "Story", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/settings-button/settings-button.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "<PERSON><PERSON><PERSON>", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/settings-button/settings-button.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "meta", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/settings-button/settings-button.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "WithCustomStyles", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/settings-button/settings-button.component.stories.ts", "type": "type alias", "linktype": "miscellaneous", "linksubtype": "typealias", "name": "Story", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "<PERSON><PERSON><PERSON>", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "meta", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "Mobile", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "Tablet", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "WithCustomTitle", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/shift-changeover/shift-changeover.stories.ts", "type": "type alias", "linktype": "miscellaneous", "linksubtype": "typealias", "name": "Story", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/shift-feedback/shift-feedback.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "<PERSON><PERSON><PERSON>", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/shift-feedback/shift-feedback.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "meta", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/shift-feedback/shift-feedback.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "PreFilled", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/shift-feedback/shift-feedback.component.stories.ts", "type": "type alias", "linktype": "miscellaneous", "linksubtype": "typealias", "name": "Story", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/sign-off-form/sign-off-form.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "<PERSON><PERSON><PERSON>", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/sign-off-form/sign-off-form.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "meta", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/sign-off-form/sign-off-form.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "WithPrefilledDate", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/sign-off-form/sign-off-form.component.stories.ts", "type": "type alias", "linktype": "miscellaneous", "linksubtype": "typealias", "name": "Story", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/task-card/task-card.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "<PERSON><PERSON><PERSON>", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/task-card/task-card.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "LongTitle", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/task-card/task-card.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "meta", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/task-card/task-card.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "SingleCategory", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/task-card/task-card.stories.ts", "type": "type alias", "linktype": "miscellaneous", "linksubtype": "typealias", "name": "Story", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/tasker/tasker.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "DoneTabActive", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/tasker/tasker.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "EmptyTodoList", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/tasker/tasker.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "meta", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/tasker/tasker.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "TodoTabActive", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/tasker/tasker.component.stories.ts", "type": "type alias", "linktype": "miscellaneous", "linksubtype": "typealias", "name": "Story", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/toggle-buttons/toggle-buttons.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "<PERSON><PERSON><PERSON>", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/toggle-buttons/toggle-buttons.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "meta", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/toggle-buttons/toggle-buttons.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "StartWithScreenThree", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/toggle-buttons/toggle-buttons.component.stories.ts", "type": "variable", "linktype": "miscellaneous", "linksubtype": "variable", "name": "StartWithScreenTwo", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}, {"filePath": "projects/shared/src/components/toggle-buttons/toggle-buttons.component.stories.ts", "type": "type alias", "linktype": "miscellaneous", "linksubtype": "typealias", "name": "Story", "coveragePercent": 0, "coverageCount": "0/1", "status": "low"}]}}