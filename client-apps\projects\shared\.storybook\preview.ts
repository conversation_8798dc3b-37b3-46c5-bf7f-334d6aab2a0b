import type { Preview } from '@storybook/angular';
import { setCompodocJson } from '@storybook/addon-docs/angular';
import 'zone.js'; // Ensure Zone.js is loaded
import docJson from '../documentation.json';
setCompodoc<PERSON><PERSON>(docJson);

const preview: Preview = {
    parameters: {
        controls: {
            matchers: {
                color: /(background|color)$/i,
                date: /Date$/i,
            },
        },
    },
};

export default preview;
