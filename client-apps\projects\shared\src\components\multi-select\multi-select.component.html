<!-- multi-select-generic.component.html -->
<div class="flex w-full" (clickOutside)="closeDropdown()">
  <div 
    class="flex flex-wrap items-center gap-2 p-2 border border-gray-300 rounded-md bg-white cursor-pointer min-h-10"
    (click)="toggleDropdown()"
  >
    @if (selectedItems.length === 0) {
      <span class="text-gray-400">{{ placeholder }}</span>
    } @else {
      @for (item of selectedItems; track item.id) {
        @if (selectedItemTemplateRef) {
          <ng-container 
            *ngTemplateOutlet="selectedItemTemplateRef; context: getContext(item, true)"
          ></ng-container>
        } @else {
          <div class="flex items-center gap-1 bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm">
            <span>{{ item['name'] || item.id }}</span>
            <button 
              type="button" 
              class="text-blue-600 hover:text-blue-800 ml-1"
              (click)="removeSelected(item); $event.stopPropagation()"
            >
              &times;
            </button>
          </div>
        }
      }
    }
    <div class="ml-auto">
      <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    </div>
  </div>

  @if (isOpen) {
    <div class="w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-96 overflow-auto">
      <div class="p-2 bg-white border-b">
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (ngModelChange)="currentPage = 1"
          (click)="$event.stopPropagation()"
          placeholder="Search items..."
          class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      
      <ul class="py-1">
        @for (item of paginatedItems; track item.id) {
          <li 
            class="px-4 py-2 cursor-pointer hover:bg-blue-50"
            [class.bg-blue-100]="isSelected(item)"
            (click)="toggleSelection(item); $event.stopPropagation()"
          >
            @if (itemTemplateRef) {
              <ng-container 
                *ngTemplateOutlet="itemTemplateRef; context: getContext(item, isSelected(item))"
              ></ng-container>
            } @else {
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <div class="font-medium">{{ item['name'] || item.id }}</div>
                  @if (item['description']) {
                    <div class="text-xs text-gray-500">{{ item['description'] }}</div>
                  }
                </div>
                @if (isSelected(item)) {
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                }
              </div>
            }
          </li>
        }
        @if (paginatedItems.length === 0) {
          <li class="px-4 py-2 text-gray-500 text-center">No items found</li>
        }
      </ul>

      @if (showPagination && totalPages > 1) {
        <div class="sticky bottom-0 bg-white border-t p-2 flex justify-between items-center">
          <button
            type="button"
            class="px-3 py-1 border rounded disabled:opacity-50"
            [disabled]="currentPage === 1"
            (click)="goToPage(currentPage - 1); $event.stopPropagation()"
          >
            Previous
          </button>
          <span class="text-sm text-gray-600">
            Page {{ currentPage }} of {{ totalPages }}
          </span>
          <button
            type="button"
            class="px-3 py-1 border rounded disabled:opacity-50"
            [disabled]="currentPage === totalPages"
            (click)="goToPage(currentPage + 1); $event.stopPropagation()"
          >
            Next
          </button>
        </div>
      }
    </div>
  }
</div>