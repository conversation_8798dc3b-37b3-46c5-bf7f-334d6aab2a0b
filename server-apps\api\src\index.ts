// import bodyParser from 'body-parser';
import cors from 'cors';
import * as express from 'express';
import helmet from 'helmet';
import { RegisterRoutes } from '../dist/routes';
import { destroyDBConnection } from './database/database.connection';
import { transactionMiddleware } from './middleware/transaction.middleware';
import { errorHandlerMiddleware } from './middleware/error-handler.middleware';
import { container } from './diod.config';
import { WsPublisher } from './shared/ws-publisher';
import { WsServer } from './server/ws.server';
import { AbstractOrderService } from './domain/order/abstract/abstract-service';

// Init web server
const app: express.Application = express.default();
const port = 3000;

// Middleware before registering routes
app.use(
    cors({
        origin: 'http://localhost:4200',
        methods: 'GET,POST,PUT,DELETE',
    })
);
app.use(express.json());
app.use(helmet());
app.use(transactionMiddleware);

// Register TSOA routes.
RegisterRoutes(app);

// Middleware after registering routes
app.use(errorHandlerMiddleware);

// Start web server.
const server = app.listen(port, () => {
    console.log(`[server]: Server is running at http://localhost:${port}`);
});

// Create websocket server and connect the client publisher to it.
const wsPublisher = container.get(WsPublisher);
const wsServer = new WsServer(server, wsPublisher);
wsPublisher.connect(wsServer.getSocketIoInstance());

// Register web socket publisher listeners.
const orderService = container.get(AbstractOrderService);
wsPublisher.addChannelSubObserver(orderService);

// Function to ensure a smooth server shutdown.
const shutDown = async () => {
    console.log('Starting server shutdown...');

    const webSocketShutdownP = wsServer.getSocketIoInstance().close((err) => {
        if (err) {
            console.error('Error while closing web socket server:', err);
            process.exit(1);
        }
        console.log('Completed Web Socket Server shutdown.');
    });

    const webShutdownP = server.close(async (err) => {
        await destroyDBConnection();
        if (err) {
            console.error('Error while closing server server:', err);
            process.exit(1);
        }
        console.log('Completed Web Server shutdown.');
    });

    await Promise.all([webSocketShutdownP, webShutdownP]);
    process.exit(0);

    // TODO Investigate if web socket server also closes.
    // wsServer.close();
};

// What system events to listen for in order to shutdown server.
process.on('SIGINT', shutDown);
process.on('SIGTERM', shutDown);
