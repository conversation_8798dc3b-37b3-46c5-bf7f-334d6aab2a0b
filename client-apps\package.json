{"name": "client-apps", "version": "0.0.1", "scripts": {"build": "ng build", "build-storybook": "ng run shared:build-storybook", "e2e": "ng e2e", "start": "ng serve", "storybook": "ng run shared:storybook", "test": "ng test", "watch": "ng build --watch --configuration development"}, "private": true, "dependencies": {"@almothafar/angular-signature-pad": "^6.0.0", "@angular/animations": "^19.0.0", "@angular/cdk": "^19.0.4", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/router": "^19.0.0", "@angular/service-worker": "^19.0.0", "@tailwindcss/postcss": "^4.1.5", "dexie": "^4.0.10", "lib": "file:../lib", "postcss": "^8.5.3", "rxjs": "~7.8.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwindcss": "^4.1.5", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.6", "@angular/cli": "^19.0.6", "@angular/compiler-cli": "^19.0.0", "@chromatic-com/storybook": "^3.2.5", "@compodoc/compodoc": "^1.1.26", "@storybook/addon-docs": "^8.6.2", "@storybook/addon-essentials": "^8.6.2", "@storybook/addon-interactions": "^8.6.2", "@storybook/addon-onboarding": "^8.6.2", "@storybook/angular": "^8.6.2", "@storybook/blocks": "^8.6.2", "@storybook/test": "^8.6.2", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.4.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "ng-packagr": "^19.2.0", "storybook": "^8.6.2", "typescript": "~5.7.2"}}