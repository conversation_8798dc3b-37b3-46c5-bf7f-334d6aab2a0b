# IMPORTANT: this only works with docker-compose, as it relies on docker-compose's context.

FROM node:lts-alpine

# Install container dependencies
# # Install `netcat`
RUN apk update && apk add netcat-openbsd

# Copy local dependencies.
COPY ./lib /usr/src/opexflow/lib

# Copy project files
COPY ./server-apps/api /usr/src/opexflow/server-apps/api

# Build local dependencies
WORKDIR /usr/src/opexflow/lib
RUN npm install

# Build api server itself
WORKDIR /usr/src/opexflow/server-apps/api
RUN npm install
RUN npm run tsoa
RUN npm run build

# Runs on port 3000
EXPOSE 3000

CMD ["npm", "run", "start"]