import type { Routes } from '@angular/router';
import { BottomSheetLayoutComponent } from '../components/layouts/bottom-sheet-layout.component';
import { NewAssignmentBottomSheeetComponent } from '../components/tasks/new-assignment-bottom-sheet.component';

export const BOTTOM_SHEET_ROUTES: Routes = [
    {
        path: '',
        component: BottomSheetLayoutComponent,
        children: [
            {
                path: 'new-assignment',
                component: NewAssignmentBottomSheeetComponent,
            },
            { path: '', redirectTo: 'new-assignment', pathMatch: 'full' }, // Default
        ],
    },
];
