<div class="button-container">
  <button *ngFor="let screen of ['screen-one', 'screen-two', 'screen-three']" [class.active]="currentScreen === screen"
    (click)="changeScreen(screen)">
    {{ screen.replace('-', ' ').toUpperCase() }}
  </button>
</div>

<div class="screen-container">
  <app-screen-one *ngIf="currentScreen === 'screen-one'"></app-screen-one>
  <app-screen-two *ngIf="currentScreen === 'screen-two'"></app-screen-two>
  <app-screen-three *ngIf="currentScreen === 'screen-three'"></app-screen-three>
</div>