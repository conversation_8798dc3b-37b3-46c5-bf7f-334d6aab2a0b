import type { UUID } from 'lib/types/common.no-deps';
import type { Condition } from 'lib/types/construct.no-deps';
import type {
    ElementContainerConditionPlain,
    ElementEntity,
} from 'lib/types/flow.no-deps';
import type { AnswerValue } from 'lib/types/task.no-deps';
import {
    evaluateNumberArrayCondition,
    evaluateNumberCondition,
    evaluateTextCondition,
} from './construct.util';

function evaluateAnswer(
    condition: Condition,
    answerValue: AnswerValue
): boolean {
    switch (answerValue.type) {
        case 'number': {
            if (condition.type !== 'number') {
                throw new Error(
                    `Expected condition to be of type 'number': ${condition.type}`
                );
            }
            return evaluateNumberCondition(condition, answerValue.value);
        }
        case 'numberArray': {
            if (condition.type !== 'numberArray') {
                throw new Error(
                    `Expected condition to be of type 'numberArray': ${condition.type}`
                );
            }
            return evaluateNumberArrayCondition(condition, answerValue.value);
        }
        case 'text': {
            if (condition.type !== 'text') {
                throw new Error(
                    `Expected condition to be of type 'text': ${condition.type}`
                );
            }
            return evaluateTextCondition(condition, answerValue.value);
        }
    }
}

export function getMatchingConditionalElements(
    conditionalElements: Record<UUID, ElementContainerConditionPlain>,
    answerValue: AnswerValue
): Record<UUID, ElementEntity> {
    // Find which conditional elements match the expected value.
    const matchingElements = Object.values(conditionalElements).filter(
        (element) => evaluateAnswer(element.condition, answerValue)
    );

    // Aggregate all the child elements from all the matching conditional elements.
    return matchingElements.reduce<Record<UUID, ElementEntity>>(
        (acc, element) => {
            for (const [id, child] of Object.entries(element.children)) {
                if (!!acc[id]) {
                    throw new Error(
                        `Found two elements with the same ID: ${id}`
                    );
                }
                acc[id] = child;
            }
            return acc;
        },
        {}
    );
}
