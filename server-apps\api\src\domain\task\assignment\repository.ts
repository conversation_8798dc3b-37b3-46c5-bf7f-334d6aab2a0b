import { Service } from 'diod';
import { type RawBuilder, sql } from 'kysely';
import { Optional, type UUID } from 'lib/types/common.no-deps';
import type {
    AssignmentFlow,
    AssignmentMetadata,
    TaskAssignment,
    TaskAssignmentStatus,
    TaskMembers,
} from 'lib/types/task.no-deps';
import { generateUUID } from 'lib/util/uuid';
import type { Database, Transaction } from '../../../database/database.types';
import { toJsonSqlValue } from '../../../database/database.util';
import {
    AbstractTaskAssignmentRepository,
    type CreateOneDTO,
    type UpdateOneDTO,
} from './abstract/abstract-repository';

type TableRow = Omit<
    Database['task_assignment'],
    | 'assignees'
    | 'created_at'
    | 'flow'
    | 'is_deleted'
    | 'last_modified_at'
    | 'status'
> & {
    assignees: TaskMembers;
    created_at: string;
    flow: AssignmentFlow;
    is_deleted: boolean;
    last_modified_at: string;
    status: TaskAssignmentStatus;
};

type UpdateOneValues = {
    assigness?: RawBuilder<TaskMembers>;
    flow?: RawBuilder<AssignmentFlow>;
    last_modified_at: RawBuilder<string>;
    last_modified_by: number;
    metadata?: RawBuilder<AssignmentMetadata>;
    status?: TaskAssignmentStatus;
};

@Service()
export class TaskAssignmentRepository extends AbstractTaskAssignmentRepository {
    fetchAllOrSince(
        db: Transaction,
        since?: string
    ): Promise<TaskAssignment[]> {
        return db
            .selectFrom('task_assignment')
            .selectAll()
            .where((eb) =>
                // If `since` is given, then return newer records compared to it.
                since ? eb('last_modified_at', '>=', since) : eb.val(true)
            )
            .execute()
            .then((rows) => rows.map(this.readRow));
    }

    fetchById(db: Transaction, id: UUID): Promise<Optional<TaskAssignment>> {
        return db
            .selectFrom('task_assignment')
            .selectAll()
            .where('id', '=', id)
            .execute()
            .then(([row]) => Optional.ofNullable(row).map(this.readRow));
    }

    fetchByIds(db: Transaction, ids: UUID[]): Promise<TaskAssignment[]> {
        return db
            .selectFrom('task_assignment')
            .selectAll()
            .where('id', 'in', ids)
            .execute()
            .then((rows) => rows.map(this.readRow));
    }

    fetchByIdsOrSince(
        db: Transaction,
        ids: UUID[],
        since?: string
    ): Promise<TaskAssignment[]> {
        return db
            .selectFrom('task_assignment')
            .selectAll()
            .where((eb) => {
                const hasIds = ids.length > 0;
                const hasSince = since !== undefined;

                return eb.or([
                    hasIds ? eb('id', 'in', ids) : eb.lit(false),
                    hasSince
                        ? eb('last_modified_at', '>=', since)
                        : eb.lit(false),
                ]);
            })
            .execute()
            .then((rows) => rows.map(this.readRow));
    }

    createOne(
        db: Transaction,
        userId: number,
        dto: CreateOneDTO
    ): Promise<UUID> {
        return db
            .insertInto('task_assignment')
            .values({
                created_by: userId,
                id: generateUUID(),
                flow: toJsonSqlValue(dto.flow),
                label: dto.label,
                last_modified_by: userId,
                metadata: dto.metadata,
                template_id: dto.templateId,
                template_version: dto.templateVersion,
            })
            .returning('id')
            .executeTakeFirstOrThrow()
            .then((result) => result.id);
    }

    createBulk(
        db: Transaction,
        assignments: TaskAssignment[]
    ): Promise<UUID[]> {
        return db
            .insertInto('task_assignment')
            .values(
                assignments.map((assignment) => ({
                    assignees: assignment.assignees,
                    created_by: assignment.createdBy,
                    flow: assignment.flow,
                    id: assignment.id,
                    label: assignment.label,
                    last_modified_by: assignment.lastModifiedBy,
                    metadata: assignment.metadata,
                    status: assignment.status,
                    template_id: assignment.templateId,
                    template_version: assignment.templateVersion,
                }))
            )
            .returning('id')
            .execute()
            .then((rows) => rows.map((row) => row.id));
    }

    updateOne(
        db: Transaction,
        id: UUID,
        userId: number,
        dto: UpdateOneDTO
    ): Promise<boolean> {
        return db
            .updateTable('task_assignment')
            .set(this.getUpdateOneValues(userId, dto))
            .where('id', '=', id)
            .executeTakeFirst()
            .then((result) => result.numUpdatedRows > 0);
    }

    updateBulk(
        db: Transaction,
        assignments: TaskAssignment[]
    ): Promise<UUID[]> {
        if (assignments.length === 0) {
            return Promise.resolve([]);
        }

        // Define what columns need to be updated in the form of a select.
        const updateSelections = assignments.map((assignment) =>
            db.selectNoFrom([
                sql<TaskMembers>`${assignment.assignees}::jsonb`.as(
                    'assignees'
                ),
                sql<UUID>`${assignment.id}::uuid`.as('id'), // Needed to match existing records
                sql<string>`${assignment.lastModifiedAt}::timestamptz`.as(
                    'last_modified_at'
                ),
                sql<number>`${assignment.lastModifiedBy}::integer`.as(
                    'last_modified_by'
                ),
                sql<TaskAssignmentStatus>`${assignment.status}::varchar`.as(
                    'status'
                ),
            ])
        );

        // Prepare a temporary update table (still a select).
        const tmpUpdateTable = updateSelections
            .slice(1)
            .reduce(
                (qb, selection) => qb.unionAll(selection),
                updateSelections[0]
            )
            .as('updates');

        // Perform the update.
        return db
            .updateTable('task_assignment as ta')
            .from(tmpUpdateTable)
            .set((eb) => ({
                assignees: eb.ref('updates.assignees'),
                last_modified_at: eb.ref('updates.last_modified_at'),
                last_modified_by: eb.ref('updates.last_modified_by'),
                status: eb.ref('updates.status'),
            }))
            .whereRef('ta.id', '=', 'updates.id')
            .returning('ta.id')
            .execute()
            .then((rows) => rows.map((row) => row.id));
    }

    deleteOne(db: Transaction, userId: number, id: UUID): Promise<boolean> {
        return db
            .updateTable('task_assignment')
            .set({
                is_deleted: true,
                last_modified_at: sql`now()`,
                last_modified_by: userId,
            })
            .where('id', '=', id)
            .executeTakeFirst()
            .then((result) => result.numUpdatedRows > 0);
    }

    private readRow = (row: TableRow): TaskAssignment => ({
        assignees: row.assignees,
        createdAt: row.created_at,
        createdBy: row.created_by,
        flow: row.flow,
        id: row.id,
        isDeleted: row.is_deleted,
        label: row.label,
        lastModifiedAt: row.last_modified_at,
        lastModifiedBy: row.last_modified_by,
        metadata: row.metadata,
        templateId: row.template_id,
        templateVersion: row.template_version,
        status: row.status,
    });

    private getUpdateOneValues = (
        userId: number,
        dto: UpdateOneDTO
    ): UpdateOneValues => {
        const values: UpdateOneValues = {
            last_modified_at: sql`now()`,
            last_modified_by: userId,
        };

        if (dto.assignees) {
            values.assigness = toJsonSqlValue(dto.assignees);
        }

        if (dto.flow) {
            values.flow = toJsonSqlValue(dto.flow);
        }

        if (dto.metadata) {
            values.metadata = toJsonSqlValue(dto.metadata);
        }

        if (dto.status) {
            values.status = dto.status;
        }

        return values;
    };
}
