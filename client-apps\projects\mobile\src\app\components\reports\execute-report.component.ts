import { CommonModule } from '@angular/common';
import { Component, Inject, Input, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { LoadingComponent } from '@shared/components/loading/loading.component';
import { FlowComponent } from '@shared/components/flow/flow.component';
import {
    REPORT_EXECUTION_SERVICE_TOKEN,
    ReportExecutionService,
} from '@shared/tokens/report-execution.token';
import type { ExecutableReport } from '@shared/types/report.type';
import type { Subscription } from 'rxjs';
import { ReportService } from '../../domain/tasks/reports/report.service';
import { ReportExecutorService } from '../../domain/tasks/reports/report-executor.service';
import { NavigationService } from '../../core/services/navigation.service';
import { PageHeaderComponent } from '../page/page-header.component';

@Component({
    selector: 'app-execute-report',
    templateUrl: './execute-report.component.html',
    styleUrl: './execute-report.component.css',
    imports: [
        CommonModule,
        FlowComponent,
        LoadingComponent,
        PageHeaderComponent,
    ],
    providers: [
        {
            provide: REPORT_EXECUTION_SERVICE_TOKEN,
            useClass: ReportExecutorService,
        },
    ],
})
export class ExecuteReportComponent implements OnInit, OnDestroy {
    report?: ExecutableReport;

    private subscriptions: Subscription[] = [];

    constructor(
        private readonly route: ActivatedRoute,
        private readonly reportService: ReportService,
        @Inject(REPORT_EXECUTION_SERVICE_TOKEN)
        private readonly reportExecutionService: ReportExecutionService,
        private readonly routeService: NavigationService
    ) {}

    async ngOnInit(): Promise<void> {
        const resolvedData: ExecutableReport =
            this.route.snapshot.data['report'];

        if (!resolvedData) {
            throw new Error(
                `Report resolved data not found: ${this.route.snapshot.data}`
            );
        }

        this.report = resolvedData;
        this.reportExecutionService.init(resolvedData.report.id);
    }

    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions.
        this.subscriptions.forEach((subscription) =>
            subscription.unsubscribe()
        );
    }

    onBack(): void {
        this.routeService.routeToAssignmentsTab();
    }

    onCompleteReport(): void {
        if (this.report) {
            const reportId = this.report.report.id;
            this.reportService.markAsComplete(reportId);
            this.routeService.routeToAssignmentsTab('done');
        }
    }
}
