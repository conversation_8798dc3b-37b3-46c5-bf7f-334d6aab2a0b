export type ArrayItemType<T extends ReadonlyArray<unknown>> =
    T extends ReadonlyArray<infer ElementType> ? ElementType : never;

export type UUID = string;

export type Nullable<T> = T | null;

export interface LastModMeta {
    createdAt: string; // ISO 8601 date-time string
    createdBy: number;
    lastModifiedAt: string; // ISO 8601 date-time string
    lastModifiedBy: number;
}

export interface ChangeSet<C, U> {
    toCreate: C[];
    toUpdate: U[];
}

export class Optional<T> {
    private constructor(private value: T | null) {}

    /**
     * Creates an Optional containing the given value.
     * @param value The value to wrap.
     */
    static of<T>(value: T): Optional<T> {
        if (value === null || value === undefined) {
            throw new Error('Value cannot be null or undefined');
        }
        return new Optional(value);
    }

    /**
     * Creates an Optional that might or might not contain a value.
     * @param value The value to wrap (can be null or undefined).
     */
    static ofNullable<T>(value: T | null | undefined): Optional<T> {
        return new Optional(value === undefined ? null : value);
    }

    /**
     * Creates an empty Optional.
     */
    static empty<T>(): Optional<T> {
        return new Optional<T>(null);
    }

    /**
     * Checks if the Optional contains a value.
     */
    isPresent(): boolean {
        return this.value !== null;
    }

    /**
     * Executes the given callback if a value is present.
     * @param callback The function to execute.
     */
    ifPresent(callback: (value: T) => void): void {
        if (this.value !== null) {
            callback(this.value);
        }
    }

    /**
     * Gets the value if present, or throws an error if empty.
     */
    get(): T {
        if (this.value === null) {
            throw new Error('No value present');
        }
        return this.value;
    }

    /**
     * Gets the value if present, or returns the given default value.
     * @param defaultValue The default value to return if empty.
     */
    orElse(defaultValue: T): T {
        return this.value !== null ? this.value : defaultValue;
    }

    /**
     * Gets the value if present, or executes a supplier to produce a value.
     * @param supplier The supplier function to produce a value.
     */
    orElseGet(supplier: () => T): T {
        return this.value !== null ? this.value : supplier();
    }

    /**
     * Gets the value if present, or throws a custom error if empty.
     * @param errorSupplier A function to produce the error to throw.
     */
    orElseThrow(errorSupplier: () => Error): T {
        if (this.value !== null) {
            return this.value;
        }
        throw errorSupplier();
    }

    /**
     * Transforms the value if present, returning a new Optional with the transformed value.
     * If the Optional is empty, returns an empty Optional.
     * @param mapper The transformation function.
     */
    map<U>(mapper: (value: T) => U): Optional<U> {
        if (this.value === null) {
            return Optional.empty<U>();
        }
        return Optional.ofNullable(mapper(this.value));
    }
}

export class Result<T, E> {
    private constructor(
        private value?: T,
        private error?: E
    ) {}

    static ok<T>(value: T): Result<T, never> {
        return new Result<T, never>(value);
    }

    static err<E>(error: E): Result<never, E> {
        return new Result<never, E>(undefined, error);
    }

    isOk(): this is Result<T, never> {
        return this.error === undefined;
    }

    isErr(): this is Result<never, E> {
        return this.value === undefined;
    }

    unwrap(): T {
        if (this.isErr()) {
            throw new Error('Attempted to unwrap an Err');
        }
        return this.value as T;
    }

    unwrapErr(): E {
        if (this.isOk()) {
            throw new Error('Attempted to unwrap an Ok');
        }
        return this.error as E;
    }
}
