# IMPORTANT: this only works with docker-compose, as it relies on docker-compose's context.

# Build stage with node
FROM node:lts-alpine as build

# # Copy local dependencies
COPY ./lib /usr/src/opexflow/lib

# # Copy main application files
COPY ./client-apps /usr/src/opexflow/client-apps

# # Remove unused sibling applications
RUN rm -rf /usr/src/opexflow/client-apps/projects/admin

# # Build local dependencies
WORKDIR /usr/src/opexflow/lib
RUN npm install

# # Build main application itself
WORKDIR /usr/src/opexflow/client-apps
RUN npm install
RUN npm run build mobile -- --configuration production

CMD [ "npm", "run", "start", "mobile", "--", "--configuration", "production", "--host", "0.0.0.0" ]