import { CommonModule } from '@angular/common';
import {
    Component,
    forwardRef,
    Input,
    OnInit,
    ViewChild,
    ViewContainerRef,
} from '@angular/core';
import type { UUID } from 'lib/types/common.no-deps';
import type {
    ElementContainerSimple,
    ElementEntity,
} from 'lib/types/flow.no-deps';
import { ElementComponent } from '../../element.component';
import { WrapperConditionalElementsComponent } from '../../wrappers/wrapper-conditional-elements.component';

@Component({
    selector: 'app-flow-container-simple',
    imports: [
        CommonModule,
        WrapperConditionalElementsComponent,
        // Loaded this way to circumvent circular dependency.
        forwardRef(() => ElementComponent),
    ],
    templateUrl: './container-simple.component.html',
})
export class FlowContainerSimpleComponent implements OnInit {
    @Input({ required: true }) element!: ElementContainerSimple;
    @Input({ required: true }) elementPath!: UUID[];
    @Input({ required: true }) indent!: number;

    @ViewChild('conditionalElementsContainer', { read: ViewContainerRef })
    conditionalElementsContainer!: ViewContainerRef;

    elements!: ElementEntity[];

    childElementIndent!: number;

    ngOnInit(): void {
        this.elements = Object.values(this.element.children);
        this.childElementIndent = this.indent + 1;
    }

    getChildElementPath(child: ElementEntity): UUID[] {
        return [...this.elementPath, child.id];
    }
}
