import { Injectable } from '@angular/core';
import { ChangeSet, UUID } from 'lib/types/common.no-deps';
import type { TaskTemplate } from 'lib/types/task.no-deps';
import { isEmptyChangeSet } from 'lib/util/change-set';
import { BehaviorSubject } from 'rxjs';
import { Transactional } from '../../../core/decorators/transactional.decorator';
import { TemplateRepository } from './template.repository';

interface TemplateChangeSet extends ChangeSet<TaskTemplate, TaskTemplate> {}

@Injectable({ providedIn: 'root' })
export class TemplateService {
    private templatesSubject = new BehaviorSubject<TaskTemplate[]>([]);

    templates$ = this.templatesSubject.asObservable();

    constructor(private readonly templateRepository: TemplateRepository) {}

    fetchAll(): Promise<void> {
        return this.loadAllTemplates();
    }

    @Transactional()
    async upsertTemplates(incomingTemplates: TaskTemplate[]): Promise<void> {
        if (incomingTemplates.length === 0) {
            return;
        }

        const changeSet = await this.createChangeSet(incomingTemplates);
        const savedIds = await this.saveChangeSet(changeSet);

        if (savedIds.length > 0) {
            this.loadTemplatesByIds(savedIds);
        }
    }

    private createChangeSet = async (
        incomingTemplates: TaskTemplate[]
    ): Promise<TemplateChangeSet> => {
        // Fetch corresponding existing templates for incoming templates.
        const incomingIds = incomingTemplates.map((template) => template.id);
        const existingTemplates =
            await this.templateRepository.fetchByIds(incomingIds);

        // Turn existing templates into a Map for faster lookup.
        const existingTemplatesMap = new Map(
            existingTemplates.map((template) => [template.id, template])
        );

        return incomingTemplates.reduce<TemplateChangeSet>(
            (acc, incomingTemplate) => {
                // Find corresponding existing template.
                const existingTemplate = existingTemplatesMap.get(
                    incomingTemplate.id
                );

                // If no corresponding existing template - it is a new template.
                if (!existingTemplate) {
                    acc.toCreate.push(incomingTemplate);
                    return acc;
                }

                // Compare incoming template with existing template based on `lastModifiedAt`
                // to determine if it's newer. If it is, then we want to update it.
                if (
                    new Date(incomingTemplate.lastModifiedAt) >
                    new Date(existingTemplate.lastModifiedAt)
                ) {
                    acc.toUpdate.push(incomingTemplate);
                }

                return acc;
            },
            { toCreate: [], toUpdate: [] }
        );
    };

    private saveChangeSet = async (
        changeSet: TemplateChangeSet
    ): Promise<UUID[]> => {
        if (isEmptyChangeSet(changeSet)) {
            return [];
        }

        const [createdIds, updatedIds] = await Promise.all([
            this.templateRepository.createBulk(changeSet.toCreate),
            this.templateRepository.updateBulk(changeSet.toUpdate),
        ]);

        return [...createdIds, ...updatedIds];
    };

    private loadAllTemplates = async (): Promise<void> => {
        const templates = await this.templateRepository.fetchAll();

        this.templatesSubject.next(templates);
    };

    private loadTemplatesByIds = async (ids: UUID[]): Promise<void> => {
        if (ids.length === 0) {
            return;
        }

        const templates = await this.templateRepository.fetchByIds(ids);

        this.templatesSubject.next(templates);
    };
}
