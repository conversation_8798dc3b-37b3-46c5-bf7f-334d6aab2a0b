import { Container<PERSON>uilder } from 'diod';
import { AbstractOrderRepository } from './domain/order/abstract/abstract-repository';
import { OrderRepository } from './domain/order/repository';
import { AbstractTaskAssignmentRepository } from './domain/task/assignment/abstract/abstract-repository';
import { AbstractTaskAssignmentService } from './domain/task/assignment/abstract/abstract-service';
import { TaskAssignmentRepository } from './domain/task/assignment/repository';
import { TaskAssignmentService } from './domain/task/assignment/service';
import { AbstractTaskReportRepository } from './domain/task/report/abstract/abstract-repository';
import { TaskReportRepository } from './domain/task/report/repository';
import { AbstractOrderService } from './domain/order/abstract/abstract-service';
import { OrderService } from './domain/order/service';
import { AbstractTaskReportService } from './domain/task/report/abstract/abstract-service';
import { TaskReportService } from './domain/task/report/service';
import { AbstractTaskTemplateRepository } from './domain/task/template/abstract/abstract-repository';
import { AbstractTaskTemplateService } from './domain/task/template/abstract/abstract-service';
import { TaskTemplateRepository } from './domain/task/template/repository';
import { TaskTemplateService } from './domain/task/template/service';
import { AbstractUserRepository } from './domain/user/abstract/user.repository';
import { AbstractUserService } from './domain/user/abstract/user.service';
import { UserRepository } from './domain/user/user.repository';
import { UserService } from './domain/user/user.service';
import { WsPublisher, WsPublisherImpl } from './shared/ws-publisher';

// Dependency injection configuration file

const builder = new ContainerBuilder();

// Register repositories
builder.register(AbstractOrderRepository).use(OrderRepository);
builder
    .register(AbstractTaskAssignmentRepository)
    .use(TaskAssignmentRepository);
builder.register(AbstractTaskReportRepository).use(TaskReportRepository);
builder.register(AbstractTaskTemplateRepository).use(TaskTemplateRepository);
builder.register(AbstractUserRepository).use(UserRepository);

// Register services
builder.register(AbstractOrderService).use(OrderService);
builder.register(AbstractTaskAssignmentService).use(TaskAssignmentService);
builder.register(AbstractTaskReportService).use(TaskReportService);
builder.register(AbstractTaskTemplateService).use(TaskTemplateService);
builder.register(AbstractUserService).use(UserService);

// Register other
builder.register(WsPublisher).useInstance(new WsPublisherImpl());

export const container = builder.build();
