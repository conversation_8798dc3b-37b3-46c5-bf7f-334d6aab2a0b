import { Injectable } from '@angular/core';
import type { ChangeSet, Optional, UUID } from 'lib/types/common.no-deps';
import type { TaskAssignment } from 'lib/types/task.no-deps';
import { isEmptyChangeSet } from 'lib/util/change-set';
import { generateUUID } from 'lib/util/uuid';
import { BehaviorSubject, map, Observable } from 'rxjs';
import { Transactional } from '../../../core/decorators/transactional.decorator';
import { UserService } from '../../users/user.service';
import { TemplateRepository } from '../templates/template.repository';
import { AssignmentRepository } from './assignment.repository';

interface AssignmentChangeSet
    extends ChangeSet<TaskAssignment, TaskAssignment> {}

@Injectable({ providedIn: 'root' })
export class AssignmentService {
    private assignmentsSubject = new BehaviorSubject<TaskAssignment[]>([]);

    assignments$ = this.assignmentsSubject.asObservable();

    constructor(
        private readonly assignmentRepository: AssignmentRepository,
        private readonly templateRepository: TemplateRepository,
        private readonly userService: UserService
    ) {}

    fetchAll(): Promise<void> {
        return this.loadAllAssignments();
    }

    fetchById(id: UUID): Promise<Optional<TaskAssignment>> {
        return this.assignmentRepository.fetchById(id);
    }

    fetchByIds(ids: UUID[]): Promise<TaskAssignment[]> {
        return this.assignmentRepository.fetchByIds(ids);
    }

    getNotStarted(): Observable<TaskAssignment[]> {
        return this.assignments$.pipe(
            map((assignments) =>
                assignments.filter(
                    (assignment) => assignment.status === 'assignmentNotStarted'
                )
            )
        );
    }

    @Transactional()
    async createAssignmentForTemplate(
        templateId: UUID
    ): Promise<TaskAssignment> {
        const templateOpt = await this.templateRepository.fetchById(templateId);
        const template = templateOpt.orElseThrow(
            () => new Error(`Expected template to exist: ${templateId}`)
        );

        // Create assignment object - using current user ID.
        const now = new Date().toISOString();
        const currentUserId = await this.userService.getCurrentUserId();
        const assignment: TaskAssignment = {
            assignees: { groups: [], users: [currentUserId] },
            createdAt: now,
            createdBy: currentUserId,
            flow: template.flow,
            id: generateUUID(),
            isDeleted: false,
            label: template.label,
            lastModifiedAt: now,
            lastModifiedBy: currentUserId,
            metadata: { origin: { type: 'adHoc' } },
            status: 'assignmentNotStarted',
            templateId: template.id,
            templateVersion: template.version,
        };

        // Save assignment to storage.
        await this.assignmentRepository.createBulk([assignment]);

        return assignment;
    }

    @Transactional()
    async updateStatusById(
        assignmentId: UUID,
        status: TaskAssignment['status']
    ): Promise<void> {
        const assignmentOpt =
            await this.assignmentRepository.fetchById(assignmentId);
        const assignment = assignmentOpt.orElseThrow(
            () => new Error(`Assignment not found: ${assignmentId}`)
        );

        return this.updateStatus(assignment, status);
    }

    @Transactional()
    async updateStatus(
        assignment: TaskAssignment,
        status: TaskAssignment['status']
    ): Promise<void> {
        const lastModifiedBy = await this.userService.getCurrentUserId();
        const lastModifiedAt = new Date().toISOString();

        return this.upsertAssignments([
            { ...assignment, lastModifiedAt, lastModifiedBy, status },
        ]);
    }

    @Transactional()
    async upsertAssignments(
        incomingAssignments: TaskAssignment[]
    ): Promise<void> {
        if (incomingAssignments.length === 0) {
            return;
        }

        const changeSet = await this.createChangeSet(incomingAssignments);
        const savedIds = await this.saveChangeSet(changeSet);

        if (savedIds.length > 0) {
            this.loadAssignmentsByIds(savedIds);
        }
    }

    private createChangeSet = async (
        incomingAssignments: TaskAssignment[]
    ): Promise<AssignmentChangeSet> => {
        // Fetch corresponding existing assignments for incoming assignments.
        const incomingIds = incomingAssignments.map(
            (assignment) => assignment.id
        );
        const existingAssignments =
            await this.assignmentRepository.fetchByIds(incomingIds);

        // Turn existing assignments into a Map for faster lookup.
        const existingAssignmentsMap = new Map(
            existingAssignments.map((assignment) => [assignment.id, assignment])
        );

        return incomingAssignments.reduce<AssignmentChangeSet>(
            (acc, incomingAssignment) => {
                // Find corresponding existing template.
                const existingAssignment = existingAssignmentsMap.get(
                    incomingAssignment.id
                );

                // If no corresponding existing template - it is a new template.
                if (!existingAssignment) {
                    acc.toCreate.push(incomingAssignment);
                    return acc;
                }

                // Compare incoming template with existing template based on `lastModifiedAt`
                // to determine if it's newer. If it is, then we want to update it.
                if (
                    new Date(incomingAssignment.lastModifiedAt) >
                    new Date(existingAssignment.lastModifiedAt)
                ) {
                    acc.toUpdate.push(incomingAssignment);
                }

                return acc;
            },
            { toCreate: [], toUpdate: [] }
        );
    };

    private saveChangeSet = async (
        changeSet: AssignmentChangeSet
    ): Promise<UUID[]> => {
        if (isEmptyChangeSet(changeSet)) {
            return [];
        }

        const [createdIds, updatedIds] = await Promise.all([
            this.assignmentRepository.createBulk(changeSet.toCreate),
            this.assignmentRepository.updateBulk(changeSet.toUpdate),
        ]);

        return [...createdIds, ...updatedIds];
    };

    private loadAllAssignments = async (): Promise<void> => {
        const assignments = await this.assignmentRepository.fetchAll();

        this.assignmentsSubject.next(assignments);
    };

    private loadAssignmentsByIds = async (ids: UUID[]): Promise<void> => {
        if (ids.length === 0) {
            return;
        }

        const assignments = await this.assignmentRepository.fetchByIds(ids);

        this.assignmentsSubject.next(assignments);
    };
}
